#!/bin/bash

# Job Application Agent Installation Script
# This script helps set up the job application agent on your system

set -e  # Exit on any error

echo "🤖 Job Application Agent Installation Script"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if Python 3.11+ is installed
check_python() {
    print_info "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 11 ]; then
            print_status "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.11+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.11+"
        exit 1
    fi
}

# Check if pip is installed
check_pip() {
    print_info "Checking pip..."
    
    if command -v pip3 &> /dev/null; then
        print_status "pip3 found"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        print_status "pip found"
        PIP_CMD="pip"
    else
        print_error "pip not found. Please install pip"
        exit 1
    fi
}

# Install Python dependencies
install_dependencies() {
    print_info "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        $PIP_CMD install -r requirements.txt
        print_status "Python dependencies installed"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Install Playwright browsers
install_playwright() {
    print_info "Installing Playwright browsers..."
    
    if command -v playwright &> /dev/null; then
        playwright install chromium --with-deps
        print_status "Playwright browsers installed"
    else
        print_warning "Playwright command not found. Trying alternative method..."
        $PYTHON_CMD -m playwright install chromium --with-deps
        if [ $? -eq 0 ]; then
            print_status "Playwright browsers installed"
        else
            print_error "Failed to install Playwright browsers"
            exit 1
        fi
    fi
}

# Check if Ollama is installed
check_ollama() {
    print_info "Checking Ollama installation..."
    
    if command -v ollama &> /dev/null; then
        print_status "Ollama found"
        
        # Check if ollama is running
        if ollama list &> /dev/null; then
            print_status "Ollama is running"
            
            # Check if llama3.2 model is available
            if ollama list | grep -q "llama3.2"; then
                print_status "llama3.2 model found"
            else
                print_warning "llama3.2 model not found"
                echo "Would you like to download it now? (This may take a while) [y/N]"
                read -r response
                if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
                    print_info "Downloading llama3.2 model..."
                    ollama pull llama3.2:latest
                    print_status "llama3.2 model downloaded"
                else
                    print_warning "You can download it later with: ollama pull llama3.2:latest"
                fi
            fi
        else
            print_warning "Ollama is not running. Please start it with: ollama serve"
        fi
    else
        print_error "Ollama not found. Please install it from: https://ollama.ai"
        echo "After installing Ollama, run: ollama pull llama3.2:latest"
        exit 1
    fi
}

# Create basic configuration
create_config() {
    print_info "Creating basic configuration..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_status "Created .env file from template"
        print_warning "Please edit .env file to configure your settings"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p data logs
    print_status "Directories created"
}

# Run quick test
run_test() {
    print_info "Running quick system test..."
    
    if $PYTHON_CMD quick_start.py &> /dev/null; then
        print_status "System test passed"
    else
        print_warning "System test had issues, but installation completed"
        print_info "You can run 'python quick_start.py' for detailed diagnostics"
    fi
}

# Main installation process
main() {
    echo
    print_info "Starting installation process..."
    echo
    
    check_python
    check_pip
    install_dependencies
    install_playwright
    check_ollama
    create_config
    create_directories
    
    echo
    print_status "Installation completed successfully!"
    echo
    print_info "Next steps:"
    echo "  1. Edit .env file to configure your settings"
    echo "  2. Run: python main.py setup"
    echo "  3. Run: python main.py start --manual (for testing)"
    echo "  4. Run: python main.py start (for scheduled operation)"
    echo
    print_info "For detailed setup instructions, see README.md"
    echo
}

# Run main function
main
