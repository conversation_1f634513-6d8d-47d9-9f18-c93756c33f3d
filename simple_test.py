#!/usr/bin/env python3
"""
Simple test script for Job Application Agent
Tests basic functionality without external dependencies
"""
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_basic_imports():
    """Test if basic Python modules work"""
    print("🔍 Testing Basic Imports...")
    
    try:
        import json
        import asyncio
        from datetime import datetime
        from pathlib import Path
        print("✅ Basic Python modules: OK")
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        return False

def test_project_structure():
    """Test if project structure is correct"""
    print("\n📁 Testing Project Structure...")
    
    required_dirs = [
        "config",
        "core", 
        "automation",
        "integrations",
        "utils",
        "scheduler"
    ]
    
    required_files = [
        "main.py",
        "requirements.txt",
        ".env.example",
        "README.md"
    ]
    
    all_good = True
    
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"✅ Directory {directory}: OK")
        else:
            print(f"❌ Directory {directory}: Missing")
            all_good = False
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ File {file}: OK")
        else:
            print(f"❌ File {file}: Missing")
            all_good = False
    
    return all_good

def test_config_loading():
    """Test configuration loading"""
    print("\n⚙️ Testing Configuration...")
    
    try:
        # Test environment loading
        from dotenv import load_dotenv
        load_dotenv()
        
        # Test settings import
        from config.settings import settings
        
        print(f"✅ Ollama Host: {settings.ollama_host}")
        print(f"✅ Target Roles: {len(settings.target_roles)} roles configured")
        print(f"✅ Max Applications: {settings.max_applications_per_day}")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_core_modules():
    """Test core module imports"""
    print("\n🧠 Testing Core Modules...")
    
    modules = [
        ("core.user_data", "User Data Management"),
        ("core.resume_parser", "Resume Parser"),
        ("utils.security", "Security Utils"),
    ]
    
    results = []
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}: OK")
            results.append(True)
        except ImportError as e:
            print(f"❌ {name}: Import failed - {e}")
            results.append(False)
        except Exception as e:
            print(f"⚠️ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_automation_modules():
    """Test automation module imports"""
    print("\n🤖 Testing Automation Modules...")
    
    modules = [
        ("automation.job_searcher", "Job Searcher"),
        ("automation.job_applicator", "Job Applicator"),
        ("scheduler.daily_runner", "Daily Runner"),
    ]
    
    results = []
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}: OK")
            results.append(True)
        except ImportError as e:
            print(f"❌ {name}: Import failed - {e}")
            results.append(False)
        except Exception as e:
            print(f"⚠️ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_integration_modules():
    """Test integration module imports"""
    print("\n🔗 Testing Integration Modules...")
    
    modules = [
        ("integrations.notion_client", "Notion Client"),
        ("integrations.email_client", "Email Client"),
    ]
    
    results = []
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}: OK")
            results.append(True)
        except ImportError as e:
            print(f"❌ {name}: Import failed - {e}")
            results.append(False)
        except Exception as e:
            print(f"⚠️ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_security():
    """Test security functionality"""
    print("\n🔒 Testing Security...")
    
    try:
        from utils.security import SecureDataManager, generate_encryption_key
        
        # Test key generation
        key = generate_encryption_key()
        print(f"✅ Encryption key generated: {key[:20]}...")
        
        # Test data manager
        manager = SecureDataManager()
        test_data = {"test": "data"}
        
        if manager.encrypt_data(test_data):
            print("✅ Data encryption: OK")
            
            decrypted = manager.decrypt_data()
            if decrypted == test_data:
                print("✅ Data decryption: OK")
            else:
                print("❌ Data decryption: Failed")
                return False
        else:
            print("❌ Data encryption: Failed")
            return False
        
        # Cleanup
        manager.clear_data()
        return True
        
    except Exception as e:
        print(f"❌ Security test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🤖 Job Application Agent - Simple Test")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Project Structure", test_project_structure),
        ("Configuration", test_config_loading),
        ("Core Modules", test_core_modules),
        ("Automation Modules", test_automation_modules),
        ("Integration Modules", test_integration_modules),
        ("Security", test_security),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"TOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! System is ready.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install Ollama: https://ollama.ai")
        print("3. Run setup: python main.py setup")
    elif passed >= total * 0.8:
        print("\n⚠️ MOSTLY WORKING with minor issues.")
        print("Install missing dependencies and try again.")
    else:
        print("\n❌ MAJOR ISSUES FOUND.")
        print("Please check the failed tests and fix issues.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
