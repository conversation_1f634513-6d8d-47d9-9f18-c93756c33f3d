#!/usr/bin/env python3
"""
Complete Job Application Agent with LinkedIn Login and Auto-Apply
"""
import sys
import asyncio
from pathlib import Path
from getpass import getpass

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def run_complete_job_agent():
    """Run the complete job application agent with LinkedIn login and auto-apply"""
    print("🤖 Complete Job Application Agent")
    print("=" * 60)
    print("Features:")
    print("✅ LinkedIn login and authentication")
    print("✅ Automated job search and filtering")
    print("✅ Easy Apply automation")
    print("✅ Company portal applications")
    print("✅ Auto-fill forms with your profile data")
    print("✅ Intelligent question answering")
    print("=" * 60)
    
    try:
        # Import required modules
        from automation.linkedin_job_applicator import LinkedInJobApplicator
        from core.user_data import UserDataManager
        
        # Load user profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found. Please run setup first.")
            return
        
        profile = user_manager.profile
        personal_info = profile.personal_info
        
        print(f"✅ Loaded profile for: {personal_info.first_name} {personal_info.last_name}")
        print(f"📧 Email: {personal_info.email}")
        print(f"📱 Phone: {personal_info.phone}")
        print(f"🎓 Education: {profile.education[0].degree} in {profile.education[0].field_of_study}")
        print(f"💼 Experience: {profile.work_experience[0].position} at {profile.work_experience[0].company}")
        print(f"🛠️ Skills: {', '.join(profile.skills[:8])}...")
        
        # Get LinkedIn credentials
        print("\n🔐 LinkedIn Credentials Required")
        print("Note: Your credentials are used only for automation and not stored permanently")
        
        linkedin_email = input("LinkedIn Email: ").strip()
        if not linkedin_email:
            print("❌ LinkedIn email is required")
            return
        
        linkedin_password = getpass("LinkedIn Password: ").strip()
        if not linkedin_password:
            print("❌ LinkedIn password is required")
            return
        
        # Convert profile to dict for the applicator
        user_profile_dict = {
            "personal_info": {
                "first_name": personal_info.first_name,
                "last_name": personal_info.last_name,
                "email": personal_info.email,
                "phone": personal_info.phone,
                "address": personal_info.address,
                "city": personal_info.city,
                "state": personal_info.state,
                "zip_code": personal_info.zip_code,
                "linkedin_url": personal_info.linkedin_url,
                "github_url": personal_info.github_url
            },
            "work_experience": [
                {
                    "position": exp.position,
                    "company": exp.company,
                    "start_date": exp.start_date,
                    "end_date": exp.end_date,
                    "description": exp.description,
                    "technologies": exp.technologies
                } for exp in profile.work_experience
            ],
            "education": [
                {
                    "degree": edu.degree,
                    "field_of_study": edu.field_of_study,
                    "institution": edu.institution,
                    "graduation_date": edu.graduation_date,
                    "gpa": edu.gpa
                } for edu in profile.education
            ],
            "skills": profile.skills,
            "certifications": profile.certifications,
            "application_answers": {
                "why_interested": profile.application_answers.why_interested,
                "greatest_strength": profile.application_answers.greatest_strength,
                "biggest_weakness": profile.application_answers.biggest_weakness,
                "career_goals": profile.application_answers.career_goals,
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "willing_to_relocate": profile.application_answers.willing_to_relocate,
                "preferred_work_arrangement": profile.application_answers.preferred_work_arrangement,
                "salary_expectation": profile.application_answers.salary_expectation
            }
        }
        
        # Initialize the LinkedIn job applicator
        applicator = LinkedInJobApplicator(user_profile_dict, linkedin_email, linkedin_password)
        
        print("\n🚀 Starting LinkedIn Job Application Process...")
        
        # Step 1: Login to LinkedIn
        print("\n1️⃣ Logging into LinkedIn...")
        login_success = await applicator.login_to_linkedin()
        
        if not login_success:
            print("❌ Failed to login to LinkedIn. Please check your credentials.")
            return
        
        print("✅ Successfully logged into LinkedIn!")
        
        # Step 2: Get application preferences
        print("\n2️⃣ Application Preferences")
        max_applications = input("Maximum applications today (default: 5): ").strip()
        max_applications = int(max_applications) if max_applications.isdigit() else 5
        
        apply_mode = input("Application mode (1=Search & Apply, 2=Specific Job URL): ").strip()
        
        if apply_mode == "2":
            # Apply to specific job
            job_url = input("Enter job URL: ").strip()
            if job_url:
                print(f"\n3️⃣ Applying to specific job: {job_url}")
                result = await applicator.apply_to_specific_job(job_url)
                
                if result["success"]:
                    print("✅ Successfully applied to the job!")
                    print(f"   Method: {result.get('method', 'Unknown')}")
                    print(f"   Applied at: {result['applied_at']}")
                else:
                    print("❌ Failed to apply to the job")
                    if "error" in result:
                        print(f"   Error: {result['error']}")
            else:
                print("❌ No job URL provided")
        else:
            # Search and apply to multiple jobs
            print(f"\n3️⃣ Searching and applying to up to {max_applications} jobs...")
            applications = await applicator.search_and_apply_jobs(max_applications)
            
            print(f"\n📊 Application Results:")
            print(f"Total applications attempted: {len(applications)}")
            
            successful = [app for app in applications if app.get("success", False)]
            print(f"Successful applications: {len(successful)}")
            
            if applications:
                print("\nDetailed Results:")
                for i, app in enumerate(applications, 1):
                    status = "✅ Success" if app.get("success", False) else "❌ Failed"
                    print(f"  {i}. {status} - Applied at {app.get('applied_at', 'Unknown')}")
                    if app.get("method"):
                        print(f"     Method: {app['method']}")
        
        print("\n" + "=" * 60)
        print("🎉 JOB APPLICATION PROCESS COMPLETED!")
        print("=" * 60)
        print("✅ LinkedIn automation successful")
        print("✅ Profile data used for auto-fill")
        print("✅ Applications submitted with your information")
        print("✅ Questions answered intelligently")
        
        print("\n📋 What the agent did:")
        print("1. Logged into your LinkedIn account")
        print("2. Searched for relevant software/cloud engineering jobs")
        print("3. Applied using Easy Apply when available")
        print("4. Filled company portal applications when needed")
        print("5. Auto-filled forms with your profile data")
        print("6. Answered application questions appropriately")
        print("7. Submitted applications on your behalf")
        
        print("\n🔒 Privacy & Security:")
        print("✅ All processing done locally with Ollama")
        print("✅ Credentials used only for automation session")
        print("✅ No data sent to external AI services")
        print("✅ Your information remains secure")
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Please ensure browser-use is installed with Python 3.11+")
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_specific_job_application():
    """Test applying to a specific job URL"""
    print("🎯 Test: Apply to Specific Job")
    print("=" * 40)
    
    # Example job URLs for testing (replace with real ones)
    test_jobs = [
        "https://www.linkedin.com/jobs/view/software-engineer-example",
        "https://www.linkedin.com/jobs/view/cloud-engineer-example",
        "https://www.linkedin.com/jobs/view/devops-engineer-example"
    ]
    
    print("Example job URLs you can test with:")
    for i, url in enumerate(test_jobs, 1):
        print(f"{i}. {url}")
    
    print("\nTo test with a real job:")
    print("1. Go to LinkedIn Jobs")
    print("2. Find a job you want to apply to")
    print("3. Copy the job URL")
    print("4. Run this script and choose option 2")
    print("5. Paste the job URL when prompted")

async def main():
    """Main function"""
    print("🤖 LinkedIn Job Application Agent")
    print("=" * 50)
    
    mode = input("Choose mode:\n1. Run complete job agent\n2. Test specific job application\nEnter choice (1 or 2): ").strip()
    
    if mode == "2":
        await test_specific_job_application()
    else:
        await run_complete_job_agent()

if __name__ == "__main__":
    asyncio.run(main())
