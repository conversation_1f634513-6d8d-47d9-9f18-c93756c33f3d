# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Notion Integration
NOTION_API_KEY=your_notion_integration_token_here
NOTION_DATABASE_ID=your_notion_database_id_here

# Email Configuration (Gmail SMTP)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587

# LinkedIn Credentials (Optional - for enhanced search)
LINKEDIN_EMAIL=<EMAIL>
LINKEDIN_PASSWORD=your_linkedin_password_here

# Security
ENCRYPTION_KEY=generate_a_32_byte_key_here
DATA_STORAGE_PATH=./data/user_data.enc

# Job Search Preferences
TARGET_ROLES=Software Engineer,Cloud Engineer,DevOps Engineer,Backend Developer
TARGET_LOCATIONS=Remote,San Francisco,New York,Seattle
EXPERIENCE_LEVEL=Entry Level,Internship
COMPANY_SIZE_PREFERENCE=Startup,Mid-size,Large
SALARY_MIN=60000
SALARY_MAX=120000

# Application Settings
MAX_APPLICATIONS_PER_DAY=10
APPLY_TO_EASY_APPLY=true
APPLY_TO_EXTERNAL_SITES=true
AUTO_ANSWER_QUESTIONS=true

# Browser Settings
HEADLESS_MODE=false
BROWSER_TIMEOUT=30
SCREENSHOT_ON_ERROR=true

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/job_agent.log

# Daily Schedule
RUN_TIME=09:00
TIMEZONE=America/Los_Angeles

# Resume and Profile
RESUME_PATH=./data/resume.pdf
COVER_LETTER_TEMPLATE_PATH=./data/cover_letter_template.txt
