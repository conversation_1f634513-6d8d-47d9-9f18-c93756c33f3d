"""
Email notification system for job application updates
"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path
from loguru import logger
from config.settings import settings

class EmailNotificationClient:
    """Handles email notifications for job application updates"""
    
    def __init__(self, 
                 email_address: Optional[str] = None,
                 email_password: Optional[str] = None,
                 smtp_server: str = "smtp.gmail.com",
                 smtp_port: int = 587):
        
        self.email_address = email_address or settings.email_address
        self.email_password = email_password or settings.email_password
        self.smtp_server = smtp_server or settings.email_smtp_server
        self.smtp_port = smtp_port or settings.email_smtp_port
        
        if not self.email_address or not self.email_password:
            logger.warning("Email credentials not provided - notifications disabled")
            self.enabled = False
        else:
            self.enabled = True
    
    def test_connection(self) -> bool:
        """Test SMTP connection"""
        if not self.enabled:
            logger.error("Email client not enabled")
            return False
        
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email_address, self.email_password)
            
            logger.info("Email connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False
    
    def send_daily_summary(self, 
                          applications_today: int,
                          successful_applications: List[Dict[str, Any]],
                          failed_applications: List[Dict[str, Any]],
                          total_applications: int) -> bool:
        """Send daily summary email"""
        
        if not self.enabled:
            logger.warning("Email notifications disabled")
            return False
        
        try:
            # Create email content
            subject = f"Job Application Agent - Daily Summary ({datetime.now().strftime('%Y-%m-%d')})"
            
            html_content = self._create_daily_summary_html(
                applications_today,
                successful_applications,
                failed_applications,
                total_applications
            )
            
            return self._send_email(
                to_email=self.email_address,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send daily summary email: {e}")
            return False
    
    def send_application_alert(self, 
                              job_title: str,
                              company: str,
                              status: str,
                              details: Optional[str] = None) -> bool:
        """Send immediate alert for important application events"""
        
        if not self.enabled:
            return False
        
        try:
            subject = f"Job Application Alert: {status} - {job_title} at {company}"
            
            html_content = f"""
            <html>
            <body>
                <h2>Job Application Alert</h2>
                <p><strong>Job:</strong> {job_title}</p>
                <p><strong>Company:</strong> {company}</p>
                <p><strong>Status:</strong> {status}</p>
                <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                {f'<p><strong>Details:</strong> {details}</p>' if details else ''}
            </body>
            </html>
            """
            
            return self._send_email(
                to_email=self.email_address,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send application alert: {e}")
            return False
    
    def send_error_notification(self, error_message: str, error_details: Optional[str] = None) -> bool:
        """Send error notification email"""
        
        if not self.enabled:
            return False
        
        try:
            subject = "Job Application Agent - Error Notification"
            
            html_content = f"""
            <html>
            <body>
                <h2 style="color: red;">Error Notification</h2>
                <p><strong>Error:</strong> {error_message}</p>
                <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                {f'<p><strong>Details:</strong><br><pre>{error_details}</pre></p>' if error_details else ''}
                <p>Please check the application logs for more information.</p>
            </body>
            </html>
            """
            
            return self._send_email(
                to_email=self.email_address,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
            return False
    
    def _create_daily_summary_html(self,
                                  applications_today: int,
                                  successful_applications: List[Dict[str, Any]],
                                  failed_applications: List[Dict[str, Any]],
                                  total_applications: int) -> str:
        """Create HTML content for daily summary email"""
        
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f8ff; padding: 20px; border-radius: 5px; }}
                .summary {{ background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .success {{ color: #28a745; }}
                .error {{ color: #dc3545; }}
                .job-item {{ margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Job Application Agent - Daily Summary</h1>
                <p><strong>Date:</strong> {datetime.now().strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="summary">
                <h2>Summary Statistics</h2>
                <ul>
                    <li><strong>Applications Today:</strong> {applications_today}</li>
                    <li><strong class="success">Successful:</strong> {len(successful_applications)}</li>
                    <li><strong class="error">Failed:</strong> {len(failed_applications)}</li>
                    <li><strong>Total Applications to Date:</strong> {total_applications}</li>
                </ul>
            </div>
        """
        
        # Successful applications
        if successful_applications:
            html += """
            <h2 class="success">✓ Successful Applications</h2>
            <table>
                <tr>
                    <th>Job Title</th>
                    <th>Company</th>
                    <th>Location</th>
                    <th>Method</th>
                </tr>
            """
            
            for app in successful_applications:
                html += f"""
                <tr>
                    <td>{app.get('title', 'N/A')}</td>
                    <td>{app.get('company', 'N/A')}</td>
                    <td>{app.get('location', 'N/A')}</td>
                    <td>{app.get('application_method', 'N/A')}</td>
                </tr>
                """
            
            html += "</table>"
        
        # Failed applications
        if failed_applications:
            html += """
            <h2 class="error">✗ Failed Applications</h2>
            <table>
                <tr>
                    <th>Job Title</th>
                    <th>Company</th>
                    <th>Error</th>
                </tr>
            """
            
            for app in failed_applications:
                html += f"""
                <tr>
                    <td>{app.get('title', 'N/A')}</td>
                    <td>{app.get('company', 'N/A')}</td>
                    <td>{app.get('error_details', 'Unknown error')}</td>
                </tr>
                """
            
            html += "</table>"
        
        html += """
            <div class="summary">
                <p><em>This is an automated email from your Job Application Agent.</em></p>
                <p>For detailed logs and more information, check your application dashboard.</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _send_email(self, 
                   to_email: str, 
                   subject: str, 
                   html_content: str,
                   attachments: Optional[List[str]] = None) -> bool:
        """Send email with HTML content and optional attachments"""
        
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = self.email_address
            message["To"] = to_email
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if Path(file_path).exists():
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {Path(file_path).name}'
                        )
                        message.attach(part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email_address, self.email_password)
                server.sendmail(self.email_address, to_email, message.as_string())
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False

if __name__ == "__main__":
    # Test the email client
    client = EmailNotificationClient()
    
    if client.test_connection():
        print("✓ Email connection successful")
        
        # Test sending a simple notification
        success = client.send_application_alert(
            job_title="Test Software Engineer",
            company="Test Company",
            status="Applied",
            details="This is a test notification from the job application agent."
        )
        
        if success:
            print("✓ Test email sent successfully")
        else:
            print("✗ Failed to send test email")
    else:
        print("✗ Email connection failed")
