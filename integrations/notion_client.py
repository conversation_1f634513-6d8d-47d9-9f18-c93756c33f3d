"""
Notion database integration for job application tracking
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from notion_client import Client
from notion_client.errors import APIResponseError
from loguru import logger
from config.settings import settings

class NotionJobTracker:
    """Manages job application tracking in Notion database"""
    
    def __init__(self, api_key: Optional[str] = None, database_id: Optional[str] = None):
        self.api_key = api_key or settings.notion_api_key
        self.database_id = database_id or settings.notion_database_id
        
        if not self.api_key or not self.database_id:
            logger.warning("Notion API key or database ID not provided")
            self.client = None
        else:
            self.client = Client(auth=self.api_key)
    
    def test_connection(self) -> bool:
        """Test connection to Notion API and database"""
        if not self.client:
            logger.error("Notion client not initialized")
            return False
        
        try:
            # Try to retrieve database info
            database = self.client.databases.retrieve(database_id=self.database_id)
            logger.info(f"Successfully connected to Notion database: {database['title'][0]['plain_text']}")
            return True
        except APIResponseError as e:
            logger.error(f"Notion API error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to Notion: {e}")
            return False
    
    def create_database_schema(self) -> bool:
        """Create or update the job tracking database schema"""
        if not self.client:
            return False
        
        try:
            # Database properties schema
            properties = {
                "Job Title": {"title": {}},
                "Company": {"rich_text": {}},
                "Status": {
                    "select": {
                        "options": [
                            {"name": "Found", "color": "blue"},
                            {"name": "Applied", "color": "yellow"},
                            {"name": "Interview", "color": "orange"},
                            {"name": "Rejected", "color": "red"},
                            {"name": "Offer", "color": "green"},
                            {"name": "Error", "color": "gray"}
                        ]
                    }
                },
                "Application Date": {"date": {}},
                "Job URL": {"url": {}},
                "Location": {"rich_text": {}},
                "Salary Range": {"rich_text": {}},
                "Application Method": {
                    "select": {
                        "options": [
                            {"name": "Easy Apply", "color": "green"},
                            {"name": "External Site", "color": "blue"},
                            {"name": "Manual", "color": "gray"}
                        ]
                    }
                },
                "Experience Level": {"rich_text": {}},
                "Skills Required": {"multi_select": {"options": []}},
                "Notes": {"rich_text": {}},
                "Error Details": {"rich_text": {}},
                "Response Received": {"checkbox": {}},
                "Interview Date": {"date": {}},
                "Follow Up Date": {"date": {}},
                "Priority": {
                    "select": {
                        "options": [
                            {"name": "High", "color": "red"},
                            {"name": "Medium", "color": "yellow"},
                            {"name": "Low", "color": "gray"}
                        ]
                    }
                }
            }
            
            # Update database properties
            self.client.databases.update(
                database_id=self.database_id,
                properties=properties
            )
            
            logger.info("Database schema updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create/update database schema: {e}")
            return False
    
    def add_job_application(self, job_data: Dict[str, Any]) -> Optional[str]:
        """Add a new job application record to Notion"""
        if not self.client:
            logger.error("Notion client not available")
            return None
        
        try:
            # Prepare properties for Notion
            properties = {
                "Job Title": {
                    "title": [{"text": {"content": job_data.get("title", "Unknown")}}]
                },
                "Company": {
                    "rich_text": [{"text": {"content": job_data.get("company", "Unknown")}}]
                },
                "Status": {
                    "select": {"name": job_data.get("status", "Found")}
                },
                "Application Date": {
                    "date": {"start": datetime.now(timezone.utc).isoformat()}
                },
                "Location": {
                    "rich_text": [{"text": {"content": job_data.get("location", "")}}]
                },
                "Application Method": {
                    "select": {"name": job_data.get("application_method", "Unknown")}
                },
                "Experience Level": {
                    "rich_text": [{"text": {"content": job_data.get("experience_level", "")}}]
                },
                "Notes": {
                    "rich_text": [{"text": {"content": job_data.get("notes", "")}}]
                }
            }
            
            # Add optional fields
            if job_data.get("url"):
                properties["Job URL"] = {"url": job_data["url"]}
            
            if job_data.get("salary_range"):
                properties["Salary Range"] = {
                    "rich_text": [{"text": {"content": job_data["salary_range"]}}]
                }
            
            if job_data.get("error_details"):
                properties["Error Details"] = {
                    "rich_text": [{"text": {"content": job_data["error_details"]}}]
                }
            
            if job_data.get("skills"):
                # Convert skills to multi-select format
                skills_options = []
                for skill in job_data["skills"][:10]:  # Limit to 10 skills
                    skills_options.append({"name": skill})
                properties["Skills Required"] = {"multi_select": skills_options}
            
            # Create the page
            response = self.client.pages.create(
                parent={"database_id": self.database_id},
                properties=properties
            )
            
            page_id = response["id"]
            logger.info(f"Job application added to Notion: {job_data.get('title')} at {job_data.get('company')}")
            return page_id
            
        except Exception as e:
            logger.error(f"Failed to add job application to Notion: {e}")
            return None
    
    def update_application_status(self, page_id: str, status: str, notes: Optional[str] = None) -> bool:
        """Update the status of an existing job application"""
        if not self.client:
            return False
        
        try:
            properties = {
                "Status": {"select": {"name": status}}
            }
            
            if notes:
                properties["Notes"] = {
                    "rich_text": [{"text": {"content": notes}}]
                }
            
            self.client.pages.update(
                page_id=page_id,
                properties=properties
            )
            
            logger.info(f"Updated application status to: {status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update application status: {e}")
            return False
    
    def get_applications_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Retrieve applications by status"""
        if not self.client:
            return []
        
        try:
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    "property": "Status",
                    "select": {"equals": status}
                }
            )
            
            applications = []
            for page in response["results"]:
                app_data = self._extract_page_data(page)
                applications.append(app_data)
            
            return applications
            
        except Exception as e:
            logger.error(f"Failed to retrieve applications by status: {e}")
            return []
    
    def get_daily_application_count(self, date: Optional[datetime] = None) -> int:
        """Get the number of applications submitted on a specific date"""
        if not self.client:
            return 0
        
        if date is None:
            date = datetime.now(timezone.utc)
        
        date_str = date.strftime("%Y-%m-%d")
        
        try:
            response = self.client.databases.query(
                database_id=self.database_id,
                filter={
                    "property": "Application Date",
                    "date": {"equals": date_str}
                }
            )
            
            return len(response["results"])
            
        except Exception as e:
            logger.error(f"Failed to get daily application count: {e}")
            return 0
    
    def _extract_page_data(self, page: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant data from a Notion page object"""
        properties = page["properties"]
        
        return {
            "id": page["id"],
            "title": self._get_title_text(properties.get("Job Title", {})),
            "company": self._get_rich_text(properties.get("Company", {})),
            "status": self._get_select_value(properties.get("Status", {})),
            "application_date": self._get_date_value(properties.get("Application Date", {})),
            "url": self._get_url_value(properties.get("Job URL", {})),
            "location": self._get_rich_text(properties.get("Location", {})),
            "notes": self._get_rich_text(properties.get("Notes", {}))
        }
    
    def _get_title_text(self, title_property: Dict) -> str:
        """Extract text from title property"""
        if title_property.get("title"):
            return title_property["title"][0]["text"]["content"]
        return ""
    
    def _get_rich_text(self, rich_text_property: Dict) -> str:
        """Extract text from rich text property"""
        if rich_text_property.get("rich_text"):
            return rich_text_property["rich_text"][0]["text"]["content"]
        return ""
    
    def _get_select_value(self, select_property: Dict) -> str:
        """Extract value from select property"""
        if select_property.get("select"):
            return select_property["select"]["name"]
        return ""
    
    def _get_date_value(self, date_property: Dict) -> Optional[str]:
        """Extract date from date property"""
        if date_property.get("date") and date_property["date"].get("start"):
            return date_property["date"]["start"]
        return None
    
    def _get_url_value(self, url_property: Dict) -> str:
        """Extract URL from URL property"""
        return url_property.get("url", "")

if __name__ == "__main__":
    # Test the Notion client
    tracker = NotionJobTracker()
    
    if tracker.test_connection():
        print("✓ Notion connection successful")
        
        # Test adding a job application
        test_job = {
            "title": "Software Engineer",
            "company": "Test Company",
            "status": "Applied",
            "url": "https://example.com/job",
            "location": "Remote",
            "application_method": "Easy Apply",
            "notes": "Test application from automation script"
        }
        
        page_id = tracker.add_job_application(test_job)
        if page_id:
            print(f"✓ Test job application added: {page_id}")
        else:
            print("✗ Failed to add test job application")
    else:
        print("✗ Notion connection failed")
