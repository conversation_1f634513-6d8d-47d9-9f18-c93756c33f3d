#!/usr/bin/env python3
"""
Test LinkedIn automation with real credentials
"""
import sys
import asyncio
from pathlib import Path
from getpass import getpass

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_linkedin_login_and_apply():
    """Test LinkedIn login and job application"""
    print("🤖 LinkedIn Job Application Test")
    print("=" * 50)
    print("This will test the complete LinkedIn automation:")
    print("✅ Login to LinkedIn")
    print("✅ Search for jobs")
    print("✅ Apply using Easy Apply")
    print("✅ Fill forms with your profile data")
    print("✅ Answer questions automatically")
    print("=" * 50)
    
    try:
        # Check if we're using Python 3.13 environment
        import sys
        if sys.version_info < (3, 11):
            print("❌ This requires Python 3.11+ for browser-use")
            print("Please run with: source venv313/bin/activate && python test_linkedin_automation.py")
            return
        
        # Import modules
        from automation.linkedin_job_applicator import LinkedInJobApplicator
        from core.user_data import UserDataManager
        
        # Load user profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return
        
        profile = user_manager.profile
        personal_info = profile.personal_info
        
        print(f"✅ Profile loaded: {personal_info.first_name} {personal_info.last_name}")
        
        # Get LinkedIn credentials
        print("\n🔐 Enter your LinkedIn credentials:")
        linkedin_email = input("LinkedIn Email: ").strip()
        linkedin_password = getpass("LinkedIn Password: ").strip()
        
        if not linkedin_email or not linkedin_password:
            print("❌ Both email and password are required")
            return
        
        # Convert profile to dict
        user_profile_dict = {
            "personal_info": {
                "first_name": personal_info.first_name,
                "last_name": personal_info.last_name,
                "email": personal_info.email,
                "phone": personal_info.phone,
                "address": personal_info.address,
                "city": personal_info.city,
                "state": personal_info.state,
                "zip_code": personal_info.zip_code,
                "linkedin_url": personal_info.linkedin_url,
                "github_url": personal_info.github_url
            },
            "skills": profile.skills,
            "application_answers": {
                "why_interested": profile.application_answers.why_interested,
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "salary_expectation": profile.application_answers.salary_expectation,
                "willing_to_relocate": profile.application_answers.willing_to_relocate,
                "preferred_work_arrangement": profile.application_answers.preferred_work_arrangement
            }
        }
        
        # Initialize applicator
        applicator = LinkedInJobApplicator(user_profile_dict, linkedin_email, linkedin_password)
        
        print("\n🚀 Starting LinkedIn automation test...")
        
        # Test 1: Login
        print("\n1️⃣ Testing LinkedIn login...")
        login_success = await applicator.login_to_linkedin()
        
        if login_success:
            print("✅ Login successful!")
        else:
            print("❌ Login failed - check credentials")
            return
        
        # Test 2: Choose test mode
        print("\n2️⃣ Choose test mode:")
        print("1. Apply to specific job URL")
        print("2. Search and apply to multiple jobs")
        
        mode = input("Enter choice (1 or 2): ").strip()
        
        if mode == "1":
            # Test specific job application
            job_url = input("Enter LinkedIn job URL: ").strip()
            if job_url:
                print(f"\n3️⃣ Applying to: {job_url}")
                result = await applicator.apply_to_specific_job(job_url)
                
                print("\n📊 Application Result:")
                print(f"Success: {'✅ Yes' if result['success'] else '❌ No'}")
                print(f"Method: {result.get('method', 'Unknown')}")
                print(f"Applied at: {result['applied_at']}")
                
                if not result['success'] and 'error' in result:
                    print(f"Error: {result['error']}")
            else:
                print("❌ No job URL provided")
        
        elif mode == "2":
            # Test job search and apply
            max_apps = input("Maximum applications (default: 3): ").strip()
            max_apps = int(max_apps) if max_apps.isdigit() else 3
            
            print(f"\n3️⃣ Searching and applying to up to {max_apps} jobs...")
            applications = await applicator.search_and_apply_jobs(max_apps)
            
            print(f"\n📊 Results:")
            print(f"Total attempts: {len(applications)}")
            successful = [app for app in applications if app.get('success', False)]
            print(f"Successful: {len(successful)}")
            
            for i, app in enumerate(applications, 1):
                status = "✅" if app.get('success', False) else "❌"
                print(f"  {i}. {status} Applied at {app.get('applied_at', 'Unknown')}")
        
        else:
            print("❌ Invalid choice")
        
        print("\n" + "=" * 50)
        print("🎉 LinkedIn automation test completed!")
        print("=" * 50)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're using the Python 3.13 environment:")
        print("source venv313/bin/activate && python test_linkedin_automation.py")
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_simple_browser_use():
    """Test basic browser-use functionality"""
    print("🧪 Testing basic browser-use functionality...")
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        
        llm = ChatOllama(
            model="llama3:latest",
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        task = """
        Go to LinkedIn (https://www.linkedin.com) and:
        1. Take a screenshot of the homepage
        2. Look for the "Sign in" button
        3. Report what you see on the page
        
        Do NOT actually sign in, just observe the page.
        """
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("✅ Basic browser-use test completed")
        print(f"Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser-use test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 LinkedIn Automation Testing Suite")
    print("=" * 60)
    
    # Check environment
    print("🔍 Checking environment...")
    print(f"Python version: {sys.version}")
    
    choice = input("\nChoose test:\n1. Basic browser-use test\n2. Full LinkedIn automation test\nEnter choice (1 or 2): ").strip()
    
    if choice == "1":
        await test_simple_browser_use()
    elif choice == "2":
        await test_linkedin_login_and_apply()
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    asyncio.run(main())
