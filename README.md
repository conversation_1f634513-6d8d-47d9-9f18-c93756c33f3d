# 🤖 Intelligent Job Application Agent

An autonomous job application system that streamlines the entire process for software engineering and cloud engineering positions. Built with **browser-use** for web automation and **Ollama** for local AI processing.

## ✨ Features

### 🔐 **Secure Data Management**
- One-time user data collection with encrypted storage
- Resume PDF parsing and analysis
- Secure credential management

### 🔍 **Intelligent Job Search**
- Automated daily searches across LinkedIn, Indeed, and Glassdoor
- AI-powered job filtering based on your preferences
- Duplicate detection and job prioritization

### 🚀 **Automated Applications**
- Easy Apply automation for LinkedIn and similar platforms
- External company website application handling
- Dynamic form filling using your stored data
- AI-powered question answering

### 📊 **Comprehensive Tracking**
- Notion database integration for application tracking
- Detailed application logs and status updates
- Daily summary emails with statistics

### 📧 **Smart Notifications**
- Gmail SMTP integration for daily summaries
- Real-time alerts for successful applications
- Error notifications and system status updates

### ⚙️ **Flexible Configuration**
- Customizable job search criteria
- Application limits and rate limiting
- Scheduled daily execution
- Manual run options

## 🛠️ Installation

### Prerequisites

1. **Python 3.11+**
2. **Ollama** - Install from [ollama.ai](https://ollama.ai)
3. **Playwright browsers** - Will be installed automatically

### Setup Steps

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd job-application-agent
pip install -r requirements.txt
```

2. **Install Playwright browsers:**
```bash
playwright install chromium --with-deps --no-shell
```

3. **Install and start Ollama:**
```bash
# Install Ollama (see ollama.ai for your platform)
# Pull a model (recommended: llama3.2)
ollama pull llama3.2:latest
```

4. **Run initial setup:**
```bash
python main.py setup
```

This will guide you through:
- Personal information collection
- Resume upload and parsing
- Notion integration setup
- Email notification configuration

## 🚀 Usage

### Start the Agent

**Scheduled Mode (Recommended):**
```bash
python main.py start
```
Runs daily at your configured time (default: 9:00 AM)

**Manual Mode:**
```bash
python main.py start --manual
```
Runs once immediately

### Other Commands

**Check system status:**
```bash
python main.py status
```

**Test components:**
```bash
python main.py test
```

**Generate encryption key:**
```bash
python main.py generate-key
```

## ⚙️ Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Notion Integration
NOTION_API_KEY=your_notion_integration_token
NOTION_DATABASE_ID=your_database_id

# Email Configuration
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Job Search Preferences
TARGET_ROLES=Software Engineer,Cloud Engineer,DevOps Engineer
TARGET_LOCATIONS=Remote,San Francisco,New York
MAX_APPLICATIONS_PER_DAY=10
```

### Notion Database Setup

1. Create a new Notion database
2. Create an integration at [notion.so/my-integrations](https://notion.so/my-integrations)
3. Share your database with the integration
4. Copy the integration token and database ID

The agent will automatically create the required database schema.

### Gmail App Password

1. Enable 2-factor authentication on your Google account
2. Generate an app password: [myaccount.google.com/apppasswords](https://myaccount.google.com/apppasswords)
3. Use this app password in your configuration

## 📁 Project Structure

```
job-application-agent/
├── main.py                 # CLI entry point
├── config/
│   └── settings.py         # Configuration management
├── core/
│   ├── user_data.py        # User data collection & management
│   └── resume_parser.py    # PDF resume parsing
├── automation/
│   ├── job_searcher.py     # Job search automation
│   └── job_applicator.py   # Job application automation
├── integrations/
│   ├── notion_client.py    # Notion database integration
│   └── email_client.py     # Email notifications
├── utils/
│   └── security.py         # Encryption & security
├── scheduler/
│   └── daily_runner.py     # Daily automation orchestrator
└── data/                   # User data storage (encrypted)
```

## 🔒 Security & Privacy

- **Local AI Processing**: Uses Ollama for local LLM processing - no data sent to external AI services
- **Encrypted Storage**: All user data is encrypted at rest using Fernet encryption
- **Secure Credentials**: Environment variables and encrypted storage for sensitive data
- **No Data Sharing**: Your personal information never leaves your system

## 🎯 Supported Platforms

- **LinkedIn** (Easy Apply + regular applications)
- **Indeed** (direct applications)
- **Glassdoor** (company redirects)
- **Company Career Pages** (external applications)

## 📊 Application Tracking

The system tracks:
- Job discovery and filtering
- Application attempts and results
- Success/failure rates
- Error details and debugging info
- Daily statistics and trends

All data is stored in your Notion database for easy review and analysis.

## 🔧 Troubleshooting

### Common Issues

**Ollama Connection Failed:**
- Ensure Ollama is running: `ollama serve`
- Check the model is installed: `ollama list`
- Verify the host URL in configuration

**Browser Automation Issues:**
- Run in non-headless mode for debugging: `HEADLESS_MODE=false`
- Check browser installation: `playwright install chromium`
- Verify network connectivity

**Application Failures:**
- Check job URLs are accessible
- Verify user data is complete
- Review error logs in `./logs/job_agent.log`

### Debug Mode

Set log level to DEBUG in your `.env`:
```bash
LOG_LEVEL=DEBUG
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool is for educational and personal use. Always comply with:
- Website terms of service
- Rate limiting and respectful automation
- Employment application best practices
- Local laws and regulations

Use responsibly and ethically. The authors are not responsible for misuse or any consequences of using this software.

## 🙏 Acknowledgments

- [browser-use](https://github.com/browser-use/browser-use) - Web automation framework
- [Ollama](https://ollama.ai) - Local LLM runtime
- [Notion](https://notion.so) - Database and tracking platform
- [Playwright](https://playwright.dev) - Browser automation library
