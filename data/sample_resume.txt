<PERSON>
Software Engineer
<EMAIL>
(555) 123-4567
123 Main Street, San Francisco, CA 94105
linkedin.com/in/johndoe
github.com/johndoe

SUMMARY
Passionate software engineer with 3 years of experience in full-stack development, cloud technologies, and DevOps practices. Skilled in Python, JavaScript, React, AWS, and Docker. Seeking entry-level to mid-level positions in software engineering and cloud engineering.

EXPERIENCE

Software Engineer | Tech Startup Inc. | 2022 - Present
• Developed and maintained web applications using React, Node.js, and Python
• Implemented CI/CD pipelines using GitHub Actions and AWS CodePipeline
• Worked with AWS services including EC2, S3, Lambda, and RDS
• Collaborated with cross-functional teams using Agile methodologies
• Reduced application load time by 40% through optimization techniques

Junior Developer | Digital Solutions LLC | 2021 - 2022
• Built responsive web interfaces using HTML, CSS, JavaScript, and React
• Developed RESTful APIs using Python Flask and Node.js Express
• Worked with databases including PostgreSQL and MongoDB
• Participated in code reviews and maintained high code quality standards

EDUCATION

Bachelor of Science in Computer Science | University of California | 2021
• Relevant Coursework: Data Structures, Algorithms, Software Engineering, Database Systems
• GPA: 3.7/4.0

TECHNICAL SKILLS

Programming Languages: Python, JavaScript, TypeScript, Java, C++
Web Technologies: React, Node.js, Express, HTML, CSS, REST APIs
Cloud & DevOps: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes, CI/CD
Databases: PostgreSQL, MongoDB, MySQL, Redis
Tools & Frameworks: Git, GitHub, VS Code, Linux, Agile/Scrum

PROJECTS

E-commerce Platform | Personal Project
• Built a full-stack e-commerce application using React and Node.js
• Implemented user authentication, payment processing, and inventory management
• Deployed on AWS using Docker containers and managed with Kubernetes

Cloud Infrastructure Automation | Hackathon Winner
• Created infrastructure-as-code solution using Terraform and AWS
• Automated deployment of scalable web applications
• Won first place at CloudTech Hackathon 2023

CERTIFICATIONS
• AWS Certified Cloud Practitioner (2023)
• Google Cloud Associate Cloud Engineer (2023)
