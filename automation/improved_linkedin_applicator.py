"""
Improved LinkedIn Job Application Automation with better LLM instructions
"""
import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger
from config.settings import settings

try:
    from browser_use import Agent
    from langchain_ollama import ChatOllama
    BROWSER_USE_AVAILABLE = True
except ImportError:
    logger.warning("browser-use not available")
    BROWSER_USE_AVAILABLE = False

class ImprovedLinkedInApplicator:
    """Improved LinkedIn automation with better LLM prompting"""
    
    def __init__(self, user_profile: Dict[str, Any], linkedin_email: str, linkedin_password: str):
        self.user_profile = user_profile
        self.linkedin_email = linkedin_email
        self.linkedin_password = linkedin_password
        
        if BROWSER_USE_AVAILABLE:
            # Use more specific model configuration for better instruction following
            self.llm = ChatOllama(
                model=settings.ollama_model,
                base_url=settings.ollama_host,
                temperature=0.0,  # Lower temperature for more deterministic behavior
                top_p=0.1,        # More focused responses
                repeat_penalty=1.1
            )
        else:
            self.llm = None
    
    async def login_to_linkedin(self) -> bool:
        """Login to LinkedIn with improved prompting"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return False
        
        try:
            # More specific and direct task instructions
            task = f"""
TASK: Login to LinkedIn

CRITICAL INSTRUCTIONS:
1. You MUST navigate to https://www.linkedin.com/login (NOT example.com)
2. You MUST enter the exact credentials provided
3. You MUST click the Sign in button
4. You MUST verify successful login

STEP-BY-STEP ACTIONS:
Step 1: Navigate to https://www.linkedin.com/login
Step 2: Find the email input field (usually has placeholder "Email or phone")
Step 3: Enter this email: {self.linkedin_email}
Step 4: Find the password input field (usually has placeholder "Password")
Step 5: Enter this password: {self.linkedin_password}
Step 6: Click the "Sign in" button
Step 7: Wait for page to load and confirm you're on LinkedIn homepage or feed

IMPORTANT NOTES:
- Do NOT go to example.com or any other site
- The LinkedIn login URL is: https://www.linkedin.com/login
- If you see a security challenge, try to handle it
- Success means you see LinkedIn feed, profile, or homepage

CREDENTIALS TO USE:
Email: {self.linkedin_email}
Password: {self.linkedin_password}

START NOW by navigating to https://www.linkedin.com/login
"""
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Check if login was successful
            success = self._check_login_success(result)
            
            if success:
                logger.info("✅ Successfully logged into LinkedIn")
            else:
                logger.error("❌ Failed to login to LinkedIn")
                logger.debug(f"Login result: {result}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error logging into LinkedIn: {e}")
            return False
    
    async def apply_to_job_url(self, job_url: str) -> Dict[str, Any]:
        """Apply to a specific LinkedIn job with improved prompting"""
        if not BROWSER_USE_AVAILABLE:
            return {"success": False, "message": "browser-use not available"}
        
        try:
            personal_info = self.user_profile.get('personal_info', {})
            app_answers = self.user_profile.get('application_answers', {})
            
            task = f"""
TASK: Apply to LinkedIn Job

JOB URL: {job_url}

CRITICAL INSTRUCTIONS:
1. Navigate to the exact job URL: {job_url}
2. Look for "Easy Apply" button and click it
3. Fill out ALL form fields with the provided information
4. Answer ALL questions using the provided answers
5. Submit the application

PERSONAL INFORMATION TO USE:
- Full Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
- Email: {personal_info.get('email', '')}
- Phone: {personal_info.get('phone', '')}
- Address: {personal_info.get('address', '')}, {personal_info.get('city', '')}, {personal_info.get('state', '')} {personal_info.get('zip_code', '')}

ANSWERS FOR COMMON QUESTIONS:
- Work Authorization: "{app_answers.get('work_authorization', 'Authorized to work in the United States')}"
- Start Date: "{app_answers.get('availability_start_date', 'Immediately available')}"
- Salary Expectation: "{app_answers.get('salary_expectation', '$80,000 - $120,000')}"
- Why Interested: "{app_answers.get('why_interested', 'Passionate about cloud computing and software engineering')}"
- Willing to Relocate: "{'Yes' if app_answers.get('willing_to_relocate', True) else 'No'}"
- Work Arrangement: "{app_answers.get('preferred_work_arrangement', 'Remote')}"

STEP-BY-STEP PROCESS:
1. Navigate to: {job_url}
2. Read the job title and company name
3. Look for "Easy Apply" button (blue button usually)
4. Click "Easy Apply"
5. Fill out each form page:
   - Enter personal information in text fields
   - Select appropriate options from dropdowns
   - Answer text questions using provided answers
   - Upload resume if there's an upload option (skip if not available)
6. Review all information
7. Click "Submit application" or "Send application"
8. Confirm application was submitted successfully

IMPORTANT:
- Be thorough and accurate
- Read each question carefully
- Use the exact information provided above
- If you encounter multiple choice questions, select the most appropriate option
- Take your time with each step

START by navigating to: {job_url}
"""
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            success = self._check_application_success(result)
            
            return {
                "success": success,
                "job_url": job_url,
                "applied_at": datetime.now().isoformat(),
                "method": "Easy Apply",
                "result_details": str(result)[:500]  # First 500 chars
            }
            
        except Exception as e:
            logger.error(f"Error applying to job: {e}")
            return {
                "success": False,
                "job_url": job_url,
                "error": str(e),
                "applied_at": datetime.now().isoformat()
            }
    
    async def search_and_apply_jobs(self, max_applications: int = 5) -> List[Dict[str, Any]]:
        """Search for jobs and apply with improved prompting"""
        if not BROWSER_USE_AVAILABLE:
            return []
        
        try:
            personal_info = self.user_profile.get('personal_info', {})
            skills = ', '.join(self.user_profile.get('skills', [])[:8])
            
            task = f"""
TASK: Search LinkedIn Jobs and Apply

CRITICAL INSTRUCTIONS:
1. Go to LinkedIn Jobs: https://www.linkedin.com/jobs/
2. Search for software engineering jobs
3. Apply to {max_applications} relevant jobs using Easy Apply
4. Fill forms with provided profile information

PROFILE SUMMARY:
- Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
- Email: {personal_info.get('email', '')}
- Phone: {personal_info.get('phone', '')}
- Skills: {skills}
- Location: {personal_info.get('city', '')}, {personal_info.get('state', '')}

SEARCH CRITERIA:
- Keywords: "Software Engineer" OR "Cloud Engineer" OR "DevOps Engineer"
- Location: "Remote" OR "California" OR "San Francisco"
- Experience: "Entry level" OR "Internship"
- Date: "Past 24 hours" OR "Past week"

STEP-BY-STEP PROCESS:
1. Navigate to https://www.linkedin.com/jobs/
2. Enter search keywords in the search box
3. Set location filter
4. Set experience level filter
5. Apply date filter for recent jobs
6. Browse job results
7. For each relevant job (up to {max_applications}):
   a. Click on the job posting
   b. Read job description to ensure it's relevant
   c. Look for "Easy Apply" button
   d. If Easy Apply available, click it
   e. Fill out application form with profile information
   f. Answer questions appropriately
   g. Submit application
   h. Move to next job

APPLICATION ANSWERS:
- Work Authorization: "Authorized to work in the United States"
- Start Date: "Immediately available"
- Salary: "$80,000 - $120,000"
- Experience Level: "Entry level"
- Why Interested: "Passionate about cloud computing and software engineering"

IMPORTANT:
- Only apply to relevant software/cloud engineering jobs
- Use Easy Apply when available
- Fill forms accurately with profile information
- Keep track of applications submitted

START by going to: https://www.linkedin.com/jobs/
"""
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Parse results
            applications = self._parse_application_results(result)
            
            logger.info(f"Job search completed. Found {len(applications)} applications.")
            
            return applications
            
        except Exception as e:
            logger.error(f"Error in job search: {e}")
            return []
    
    def _check_login_success(self, result: Any) -> bool:
        """Check if LinkedIn login was successful"""
        result_str = str(result).lower()
        
        success_indicators = [
            'linkedin.com/feed',
            'linkedin.com/in/',
            'linkedin home',
            'successfully logged',
            'welcome back',
            'linkedin dashboard',
            'profile picture',
            'connections'
        ]
        
        failure_indicators = [
            'incorrect email',
            'wrong password',
            'login failed',
            'sign in to linkedin',
            'enter your email',
            'example.com'  # If it went to wrong site
        ]
        
        # Check for failure first
        if any(indicator in result_str for indicator in failure_indicators):
            return False
        
        # Check for success
        return any(indicator in result_str for indicator in success_indicators)
    
    def _check_application_success(self, result: Any) -> bool:
        """Check if job application was successful"""
        result_str = str(result).lower()
        
        success_indicators = [
            'application submitted',
            'successfully applied',
            'application sent',
            'thank you for applying',
            'application received',
            'application complete',
            'submitted successfully',
            'your application has been sent'
        ]
        
        return any(indicator in result_str for indicator in success_indicators)
    
    def _parse_application_results(self, result: Any) -> List[Dict[str, Any]]:
        """Parse application results"""
        applications = []
        result_str = str(result)
        
        # Look for application indicators
        if any(word in result_str.lower() for word in ['applied', 'application', 'submitted']):
            applications.append({
                "success": self._check_application_success(result),
                "applied_at": datetime.now().isoformat(),
                "method": "LinkedIn Search",
                "details": result_str[:300]
            })
        
        return applications

# Test function with better error handling
async def test_improved_linkedin():
    """Test the improved LinkedIn applicator"""
    print("🧪 Testing Improved LinkedIn Automation")
    print("=" * 50)
    
    try:
        from core.user_data import UserDataManager
        
        # Load user profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return
        
        profile = user_manager.profile
        
        # Convert to dict
        user_profile_dict = {
            "personal_info": {
                "first_name": profile.personal_info.first_name,
                "last_name": profile.personal_info.last_name,
                "email": profile.personal_info.email,
                "phone": profile.personal_info.phone,
                "city": profile.personal_info.city,
                "state": profile.personal_info.state
            },
            "application_answers": {
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "salary_expectation": profile.application_answers.salary_expectation,
                "why_interested": profile.application_answers.why_interested,
                "willing_to_relocate": profile.application_answers.willing_to_relocate,
                "preferred_work_arrangement": profile.application_answers.preferred_work_arrangement
            },
            "skills": profile.skills
        }
        
        # Test credentials (replace with real ones)
        test_email = "<EMAIL>"
        test_password = "your_password"
        
        applicator = ImprovedLinkedInApplicator(user_profile_dict, test_email, test_password)
        
        print("✅ Improved LinkedIn applicator initialized")
        print("📝 Key improvements:")
        print("  - More specific navigation instructions")
        print("  - Lower temperature for better instruction following")
        print("  - Clearer step-by-step prompts")
        print("  - Better error detection")
        
        # In real use:
        # login_success = await applicator.login_to_linkedin()
        # if login_success:
        #     applications = await applicator.search_and_apply_jobs(3)
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_improved_linkedin())
