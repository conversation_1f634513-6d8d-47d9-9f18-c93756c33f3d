"""
Complete LinkedIn Job Application Automation with browser-use
Handles login, job search, Easy Apply, and company portal applications
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from loguru import logger
from config.settings import settings

try:
    from browser_use import Agent
    from langchain_ollama import ChatOllama
    BROWSER_USE_AVAILABLE = True
except ImportError:
    logger.warning("browser-use not available")
    BROWSER_USE_AVAILABLE = False

class LinkedInJobApplicator:
    """Complete LinkedIn job application automation"""
    
    def __init__(self, user_profile: Dict[str, Any], linkedin_email: str, linkedin_password: str):
        self.user_profile = user_profile
        self.linkedin_email = linkedin_email
        self.linkedin_password = linkedin_password
        self.applications_today = 0
        
        if BROWSER_USE_AVAILABLE:
            self.llm = ChatOllama(
                model=settings.ollama_model,
                base_url=settings.ollama_host,
                temperature=0.1
            )
        else:
            self.llm = None
    
    async def login_to_linkedin(self) -> bool:
        """Login to LinkedIn using browser-use"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return False
        
        try:
            task = f"""
            Go to LinkedIn login page (https://www.linkedin.com/login) and log in with these credentials:
            
            Email: {self.linkedin_email}
            Password: {self.linkedin_password}
            
            Steps:
            1. Navigate to https://www.linkedin.com/login
            2. Find the email/username input field and enter: {self.linkedin_email}
            3. Find the password input field and enter: {self.linkedin_password}
            4. Click the "Sign in" button
            5. Wait for the page to load and confirm successful login
            6. If there are any security challenges (like "Verify it's you"), handle them appropriately
            7. Navigate to the LinkedIn homepage to confirm login success
            
            Important: Be careful with the credentials and make sure the login is successful.
            """
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Check if login was successful
            success = self._check_login_success(result)
            
            if success:
                logger.info("Successfully logged into LinkedIn")
            else:
                logger.error("Failed to login to LinkedIn")
            
            return success
            
        except Exception as e:
            logger.error(f"Error logging into LinkedIn: {e}")
            return False
    
    async def search_and_apply_jobs(self, max_applications: int = 10) -> List[Dict[str, Any]]:
        """Search for jobs and apply to them"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return []
        
        try:
            personal_info = self.user_profile.get('personal_info', {})
            skills = self.user_profile.get('skills', [])
            
            task = f"""
            Search for software engineering and cloud engineering jobs on LinkedIn and apply to them.
            
            Profile Information:
            - Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
            - Email: {personal_info.get('email', '')}
            - Phone: {personal_info.get('phone', '')}
            - Location: {personal_info.get('city', '')}, {personal_info.get('state', '')}
            - Skills: {', '.join(skills[:10])}
            
            Steps:
            1. Go to LinkedIn Jobs (https://www.linkedin.com/jobs/)
            2. Search for jobs with keywords: "Software Engineer" OR "Cloud Engineer" OR "DevOps Engineer"
            3. Filter by:
               - Location: "Remote" OR "California" OR "San Francisco"
               - Experience level: "Entry level" OR "Internship"
               - Date posted: "Past 24 hours" or "Past week"
            
            4. For each relevant job (up to {max_applications} applications):
               a. Click on the job posting
               b. Read the job description to ensure it matches the profile
               c. Look for "Easy Apply" button
               d. If Easy Apply is available, click it and proceed with application
               e. If no Easy Apply, look for "Apply" button that might redirect to company website
            
            5. For Easy Apply applications:
               a. Fill out all required fields with the profile information
               b. Upload resume if prompted (skip if not available)
               c. Answer any questions using the profile data:
                  - Why interested: "Passionate about cloud computing and software engineering"
                  - Experience level: "Entry level with internship experience"
                  - Work authorization: "Authorized to work in the US"
                  - Availability: "Immediately available"
                  - Salary expectations: "$80,000 - $120,000"
               d. Submit the application
            
            6. For company portal applications:
               a. Click the apply button to go to company website
               b. Fill out the application form with profile information
               c. Answer questions appropriately
               d. Submit the application
            
            7. Keep track of each application attempt and result
            
            Important: Be thorough and accurate with form filling. Take time between applications.
            """
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Parse application results
            applications = self._parse_application_results(result)
            
            logger.info(f"Completed job application process. Applied to {len(applications)} jobs.")
            
            return applications
            
        except Exception as e:
            logger.error(f"Error in job search and application: {e}")
            return []
    
    async def handle_application_questions(self, questions: List[str]) -> Dict[str, str]:
        """Handle application questions intelligently"""
        from config.linkedin_config import ApplicationAnswers

        answers = {}
        for question in questions:
            answer = ApplicationAnswers.get_answer_for_question(question, self.user_profile)
            answers[question] = answer

        return answers

    async def apply_to_specific_job(self, job_url: str) -> Dict[str, Any]:
        """Apply to a specific job URL"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return {"success": False, "message": "browser-use not available"}
        
        try:
            personal_info = self.user_profile.get('personal_info', {})
            app_answers = self.user_profile.get('application_answers', {})
            work_exp = self.user_profile.get('work_experience', [])
            education = self.user_profile.get('education', [])
            
            # Get work experience details
            current_job = work_exp[0] if work_exp else {}
            current_education = education[0] if education else {}
            
            task = f"""
            Apply to the specific job at this URL: {job_url}
            
            Use this detailed profile information to fill out the application:
            
            PERSONAL INFORMATION:
            - Full Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
            - Email: {personal_info.get('email', '')}
            - Phone: {personal_info.get('phone', '')}
            - Address: {personal_info.get('address', '')}, {personal_info.get('city', '')}, {personal_info.get('state', '')} {personal_info.get('zip_code', '')}
            - LinkedIn: {personal_info.get('linkedin_url', '')}
            - GitHub: {personal_info.get('github_url', '')}
            
            WORK EXPERIENCE:
            - Current/Recent Position: {current_job.get('position', '')}
            - Company: {current_job.get('company', '')}
            - Duration: {current_job.get('start_date', '')} to {current_job.get('end_date', 'Present')}
            - Description: {current_job.get('description', '')}
            
            EDUCATION:
            - Degree: {current_education.get('degree', '')}
            - Field: {current_education.get('field_of_study', '')}
            - Institution: {current_education.get('institution', '')}
            - Graduation: {current_education.get('graduation_date', '')}
            - GPA: {current_education.get('gpa', '')}
            
            APPLICATION ANSWERS:
            - Why interested: {app_answers.get('why_interested', '')}
            - Greatest strength: {app_answers.get('greatest_strength', '')}
            - Career goals: {app_answers.get('career_goals', '')}
            - Work authorization: {app_answers.get('work_authorization', '')}
            - Start date: {app_answers.get('availability_start_date', '')}
            - Willing to relocate: {"Yes" if app_answers.get('willing_to_relocate', False) else "No"}
            - Work arrangement: {app_answers.get('preferred_work_arrangement', '')}
            - Salary expectation: {app_answers.get('salary_expectation', '')}
            
            DETAILED STEPS:
            1. Navigate to the job URL: {job_url}
            2. Read the job posting to understand requirements
            3. Look for application method:
               - "Easy Apply" button (preferred)
               - "Apply" button (may redirect to company site)
               - External application link
            
            4. If Easy Apply:
               a. Click "Easy Apply"
               b. Fill out personal information fields
               c. Upload resume if there's an upload option
               d. Answer all questions using the provided answers
               e. For dropdown/multiple choice questions, select the most appropriate option
               f. Review and submit
            
            5. If Company Portal:
               a. Click apply button to go to company website
               b. Create account if required using the email
               c. Fill out complete application form
               d. Upload resume and cover letter if requested
               e. Answer all questions thoroughly
               f. Submit application
            
            6. For any questions not covered above:
               - Experience level: "Entry level" or "1-3 years"
               - Visa sponsorship: "No sponsorship required" (if work authorized)
               - Notice period: "Immediately available" or "2 weeks"
               - Preferred start date: "As soon as possible"
               - Salary range: "$80,000 - $120,000" or "Competitive"
            
            7. Confirm application submission and note any confirmation details
            
            Be very careful and thorough. Take time to read questions and provide appropriate answers.
            """
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Check application success
            success = self._check_application_success(result)
            
            application_result = {
                "success": success,
                "job_url": job_url,
                "applied_at": datetime.now().isoformat(),
                "method": "Easy Apply" if "easy apply" in str(result).lower() else "Company Portal",
                "result_details": str(result)
            }
            
            if success:
                self.applications_today += 1
                logger.info(f"Successfully applied to job: {job_url}")
            else:
                logger.warning(f"Failed to apply to job: {job_url}")
            
            return application_result
            
        except Exception as e:
            logger.error(f"Error applying to specific job: {e}")
            return {
                "success": False,
                "job_url": job_url,
                "applied_at": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def _check_login_success(self, result: Any) -> bool:
        """Check if LinkedIn login was successful"""
        result_str = str(result).lower()
        
        success_indicators = [
            'linkedin.com/feed',
            'linkedin.com/in/',
            'successfully logged in',
            'welcome back',
            'home page',
            'dashboard'
        ]
        
        failure_indicators = [
            'incorrect email',
            'wrong password',
            'login failed',
            'error',
            'try again'
        ]
        
        # Check for failure first
        if any(indicator in result_str for indicator in failure_indicators):
            return False
        
        # Check for success
        return any(indicator in result_str for indicator in success_indicators)
    
    def _check_application_success(self, result: Any) -> bool:
        """Check if job application was successful"""
        result_str = str(result).lower()
        
        success_indicators = [
            'application submitted',
            'successfully applied',
            'application sent',
            'thank you for applying',
            'application received',
            'application complete',
            'submitted successfully'
        ]
        
        return any(indicator in result_str for indicator in success_indicators)
    
    def _parse_application_results(self, result: Any) -> List[Dict[str, Any]]:
        """Parse application results from agent output"""
        applications = []
        
        try:
            result_str = str(result)
            
            # Look for application patterns
            if 'applied' in result_str.lower():
                # Create a basic application record
                applications.append({
                    "success": self._check_application_success(result),
                    "applied_at": datetime.now().isoformat(),
                    "method": "LinkedIn Search",
                    "details": result_str[:500]  # First 500 chars
                })
            
        except Exception as e:
            logger.error(f"Error parsing application results: {e}")
        
        return applications

# Test function
async def test_linkedin_applicator():
    """Test the LinkedIn job applicator"""
    # This would use real credentials in practice
    test_email = "<EMAIL>"
    test_password = "your_linkedin_password"
    
    # Mock user profile
    user_profile = {
        "personal_info": {
            "first_name": "Hemanth Kiran Reddy",
            "last_name": "Polu",
            "email": "<EMAIL>",
            "phone": "(*************",
            "city": "San Bernardino",
            "state": "California"
        },
        "skills": ["AWS", "Azure", "Python", "C++", "Cloud Security"],
        "application_answers": {
            "why_interested": "Passionate about cloud computing and software engineering",
            "work_authorization": "Authorized to work in the US",
            "availability_start_date": "Immediately available"
        }
    }
    
    applicator = LinkedInJobApplicator(user_profile, test_email, test_password)
    
    print("🔍 Testing LinkedIn Job Applicator...")
    print("Note: This test requires real LinkedIn credentials")
    
    # In a real scenario, you would:
    # 1. Login: await applicator.login_to_linkedin()
    # 2. Apply to jobs: await applicator.search_and_apply_jobs(max_applications=5)
    
    print("✅ LinkedIn applicator ready for use")

if __name__ == "__main__":
    asyncio.run(test_linkedin_applicator())
