#!/usr/bin/env python3
"""
Reliable LinkedIn Job Application Bot - Production Ready
Uses Selenium for reliable automation instead of browser-use
"""
import time
import random
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from loguru import logger
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

class ReliableLinkedInBot:
    """Production-ready LinkedIn job application bot"""
    
    def __init__(self, user_profile: Dict[str, Any]):
        self.user_profile = user_profile
        self.driver = None
        self.wait = None
        self.applications_today = 0
        self.max_applications = 10
        self.applied_jobs = set()
        
    def setup_driver(self) -> None:
        """Setup Chrome driver with proper options"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("✅ Chrome driver setup successful")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup driver: {e}")
            raise
    
    def login_to_linkedin(self, email: str, password: str) -> bool:
        """Login to LinkedIn with credentials"""
        try:
            logger.info("🔐 Starting LinkedIn login...")
            
            # Navigate to LinkedIn login
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(2)
            
            # Enter email
            email_field = self.wait.until(EC.presence_of_element_located((By.ID, "username")))
            email_field.clear()
            email_field.send_keys(email)
            
            # Enter password
            password_field = self.driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(password)
            
            # Click sign in
            sign_in_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            sign_in_button.click()
            
            # Wait for login to complete
            time.sleep(5)
            
            # Check if login was successful
            if "feed" in self.driver.current_url or "linkedin.com/in/" in self.driver.current_url:
                logger.info("✅ LinkedIn login successful")
                return True
            else:
                logger.error("❌ LinkedIn login failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Login error: {e}")
            return False
    
    def search_jobs(self, keywords: List[str], location: str = "Remote") -> List[Dict[str, Any]]:
        """Search for jobs on LinkedIn"""
        try:
            logger.info(f"🔍 Searching for jobs: {keywords} in {location}")
            
            # Navigate to LinkedIn Jobs
            self.driver.get("https://www.linkedin.com/jobs/")
            time.sleep(3)
            
            # Try multiple selectors for search box
            search_box = None
            search_selectors = [
                "//input[contains(@placeholder, 'Search jobs')]",
                "//input[@aria-label='Search jobs']",
                "//input[contains(@class, 'jobs-search-box__text-input')]",
                "//input[@id='jobs-search-box-keyword-id-ember']"
            ]

            for selector in search_selectors:
                try:
                    search_box = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    break
                except TimeoutException:
                    continue

            if search_box:
                search_box.clear()
                search_box.send_keys(" OR ".join(keywords))

            # Try multiple selectors for location box
            location_selectors = [
                "//input[contains(@placeholder, 'City, state, or zip code')]",
                "//input[@aria-label='City, state, or zip code']",
                "//input[contains(@class, 'jobs-search-box__text-input')][2]"
            ]

            for selector in location_selectors:
                try:
                    location_box = self.driver.find_element(By.XPATH, selector)
                    location_box.clear()
                    location_box.send_keys(location)
                    break
                except NoSuchElementException:
                    continue

            # Try multiple selectors for search button
            search_selectors = [
                "//button[contains(@class, 'jobs-search-box__submit-button')]",
                "//button[@aria-label='Search']",
                "//button[contains(text(), 'Search')]"
            ]

            for selector in search_selectors:
                try:
                    search_button = self.driver.find_element(By.XPATH, selector)
                    search_button.click()
                    break
                except NoSuchElementException:
                    continue
            
            time.sleep(5)
            
            # Apply filters
            self._apply_job_filters()
            
            # Get job listings
            jobs = self._extract_job_listings()
            
            logger.info(f"✅ Found {len(jobs)} jobs")
            return jobs
            
        except Exception as e:
            logger.error(f"❌ Job search error: {e}")
            return []
    
    def _apply_job_filters(self) -> None:
        """Apply filters for entry-level, recent jobs"""
        try:
            # Click on Experience Level filter
            try:
                experience_filter = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Experience level')]")
                experience_filter.click()
                time.sleep(2)
                
                # Select Entry level and Internship
                entry_level = self.driver.find_element(By.XPATH, "//label[contains(text(), 'Entry level')]")
                entry_level.click()
                
                internship = self.driver.find_element(By.XPATH, "//label[contains(text(), 'Internship')]")
                internship.click()
                
                # Apply filter
                apply_button = self.driver.find_element(By.XPATH, "//button[contains(@class, 'filter__submit-button')]")
                apply_button.click()
                time.sleep(3)
                
            except NoSuchElementException:
                logger.warning("⚠️ Experience level filter not found")
            
            # Click on Date Posted filter
            try:
                date_filter = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Date posted')]")
                date_filter.click()
                time.sleep(2)
                
                # Select Past 24 hours
                past_24h = self.driver.find_element(By.XPATH, "//label[contains(text(), 'Past 24 hours')]")
                past_24h.click()
                
                # Apply filter
                apply_button = self.driver.find_element(By.XPATH, "//button[contains(@class, 'filter__submit-button')]")
                apply_button.click()
                time.sleep(3)
                
            except NoSuchElementException:
                logger.warning("⚠️ Date posted filter not found")
                
        except Exception as e:
            logger.warning(f"⚠️ Filter application error: {e}")
    
    def _extract_job_listings(self) -> List[Dict[str, Any]]:
        """Extract job listings from search results"""
        jobs = []
        try:
            # Get job cards
            job_cards = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'job-search-card')]")
            
            for card in job_cards[:20]:  # Limit to first 20 jobs
                try:
                    # Extract job details
                    title_element = card.find_element(By.XPATH, ".//h3[contains(@class, 'base-search-card__title')]//a")
                    title = title_element.text.strip()
                    job_url = title_element.get_attribute("href")
                    
                    company_element = card.find_element(By.XPATH, ".//h4[contains(@class, 'base-search-card__subtitle')]//a")
                    company = company_element.text.strip()
                    
                    location_element = card.find_element(By.XPATH, ".//span[contains(@class, 'job-search-card__location')]")
                    location = location_element.text.strip()
                    
                    # Check for Easy Apply
                    easy_apply = len(card.find_elements(By.XPATH, ".//span[contains(text(), 'Easy Apply')]")) > 0
                    
                    job = {
                        "title": title,
                        "company": company,
                        "location": location,
                        "url": job_url,
                        "easy_apply": easy_apply,
                        "found_date": datetime.now().isoformat()
                    }
                    
                    jobs.append(job)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Error extracting job details: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ Error extracting job listings: {e}")
        
        return jobs
    
    def apply_to_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to a specific job"""
        try:
            logger.info(f"📝 Applying to: {job['title']} at {job['company']}")
            
            # Navigate to job page
            self.driver.get(job["url"])
            time.sleep(3)
            
            # Check if already applied
            if self._is_already_applied():
                logger.info("⚠️ Already applied to this job")
                return {"success": False, "reason": "Already applied"}
            
            # Look for Easy Apply button
            if job.get("easy_apply", False):
                return self._apply_easy_apply(job)
            else:
                return self._apply_external(job)
                
        except Exception as e:
            logger.error(f"❌ Application error: {e}")
            return {"success": False, "error": str(e)}
    
    def _is_already_applied(self) -> bool:
        """Check if already applied to this job"""
        try:
            applied_elements = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'Applied')]")
            return len(applied_elements) > 0
        except:
            return False
    
    def _apply_easy_apply(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """Apply using Easy Apply"""
        try:
            # Click Easy Apply button
            easy_apply_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'jobs-apply-button') and contains(., 'Easy Apply')]")))
            easy_apply_button.click()
            time.sleep(2)
            
            # Fill application form
            success = self._fill_application_form()
            
            if success:
                # Submit application
                submit_success = self._submit_application()
                
                if submit_success:
                    self.applications_today += 1
                    logger.info(f"✅ Successfully applied to {job['title']}")
                    return {"success": True, "method": "Easy Apply"}
                else:
                    return {"success": False, "reason": "Failed to submit"}
            else:
                return {"success": False, "reason": "Failed to fill form"}
                
        except Exception as e:
            logger.error(f"❌ Easy Apply error: {e}")
            return {"success": False, "error": str(e)}
    
    def _fill_application_form(self) -> bool:
        """Fill out the application form with profile data"""
        try:
            personal_info = self.user_profile.get("personal_info", {})
            app_answers = self.user_profile.get("application_answers", {})
            
            # Wait for form to load
            time.sleep(2)
            
            # Fill text fields
            self._fill_text_fields(personal_info)
            
            # Handle dropdowns and selections
            self._handle_dropdowns(app_answers)
            
            # Answer text questions
            self._answer_text_questions(app_answers)
            
            # Handle file uploads (resume)
            self._handle_file_uploads()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Form filling error: {e}")
            return False
    
    def _fill_text_fields(self, personal_info: Dict[str, Any]) -> None:
        """Fill basic text fields"""
        try:
            # Phone number
            phone_fields = self.driver.find_elements(By.XPATH, "//input[@type='tel' or contains(@placeholder, 'phone') or contains(@name, 'phone')]")
            for field in phone_fields:
                if not field.get_attribute("value"):
                    field.clear()
                    field.send_keys(personal_info.get("phone", ""))
                    break
            
            # Email (usually pre-filled)
            email_fields = self.driver.find_elements(By.XPATH, "//input[@type='email' or contains(@placeholder, 'email') or contains(@name, 'email')]")
            for field in email_fields:
                if not field.get_attribute("value"):
                    field.clear()
                    field.send_keys(personal_info.get("email", ""))
                    break
            
        except Exception as e:
            logger.warning(f"⚠️ Text field filling error: {e}")
    
    def _handle_dropdowns(self, app_answers: Dict[str, Any]) -> None:
        """Handle dropdown selections"""
        try:
            # Work authorization dropdown
            work_auth_dropdowns = self.driver.find_elements(By.XPATH, "//select[contains(@name, 'authorization') or contains(@name, 'visa') or contains(@name, 'work')]")
            for dropdown in work_auth_dropdowns:
                select = Select(dropdown)
                options = [option.text.lower() for option in select.options]
                
                # Look for "authorized" or "no sponsorship" options
                for i, option_text in enumerate(options):
                    if "authorized" in option_text or "no sponsorship" in option_text or "citizen" in option_text:
                        select.select_by_index(i)
                        break
            
            # Experience level dropdown
            exp_dropdowns = self.driver.find_elements(By.XPATH, "//select[contains(@name, 'experience') or contains(@name, 'level')]")
            for dropdown in exp_dropdowns:
                select = Select(dropdown)
                options = [option.text.lower() for option in select.options]
                
                # Look for entry level options
                for i, option_text in enumerate(options):
                    if "entry" in option_text or "0-1" in option_text or "1-3" in option_text:
                        select.select_by_index(i)
                        break
            
        except Exception as e:
            logger.warning(f"⚠️ Dropdown handling error: {e}")
    
    def _answer_text_questions(self, app_answers: Dict[str, Any]) -> None:
        """Answer text area questions"""
        try:
            # Find text areas and text inputs for questions
            text_areas = self.driver.find_elements(By.XPATH, "//textarea | //input[@type='text'][not(@name='email') and not(@name='phone')]")
            
            for text_area in text_areas:
                placeholder = text_area.get_attribute("placeholder") or ""
                name = text_area.get_attribute("name") or ""
                label_text = ""
                
                # Try to find associated label
                try:
                    label_element = text_area.find_element(By.XPATH, "./preceding-sibling::label | ./parent::*/preceding-sibling::label")
                    label_text = label_element.text.lower()
                except:
                    pass
                
                question_text = (placeholder + " " + name + " " + label_text).lower()
                
                # Determine appropriate answer
                answer = self._get_answer_for_question(question_text, app_answers)
                
                if answer and not text_area.get_attribute("value"):
                    text_area.clear()
                    text_area.send_keys(answer)
            
        except Exception as e:
            logger.warning(f"⚠️ Text question answering error: {e}")
    
    def _get_answer_for_question(self, question_text: str, app_answers: Dict[str, Any]) -> str:
        """Get appropriate answer for a question"""
        question_text = question_text.lower()
        
        if any(keyword in question_text for keyword in ["why", "interest", "motivation"]):
            return app_answers.get("why_interested", "I am passionate about cloud computing and software engineering.")
        
        elif any(keyword in question_text for keyword in ["salary", "compensation", "pay"]):
            return app_answers.get("salary_expectation", "$80,000 - $120,000")
        
        elif any(keyword in question_text for keyword in ["start", "availability", "when"]):
            return app_answers.get("availability_start_date", "Immediately available")
        
        elif any(keyword in question_text for keyword in ["experience", "years"]):
            return "Entry level with relevant internship and project experience"
        
        elif any(keyword in question_text for keyword in ["cover", "letter"]):
            return app_answers.get("why_interested", "I bring strong technical skills and eagerness to learn.")
        
        return ""
    
    def _handle_file_uploads(self) -> None:
        """Handle resume file uploads"""
        try:
            file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
            resume_path = self.user_profile.get("resume_path")
            
            if file_inputs and resume_path and Path(resume_path).exists():
                file_inputs[0].send_keys(resume_path)
                time.sleep(2)
                
        except Exception as e:
            logger.warning(f"⚠️ File upload error: {e}")
    
    def _submit_application(self) -> bool:
        """Submit the application"""
        try:
            # Look for submit/send button
            submit_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Submit') or contains(text(), 'Send') or contains(text(), 'Apply')]")
            
            for button in submit_buttons:
                if button.is_enabled():
                    button.click()
                    time.sleep(3)
                    
                    # Check for confirmation
                    confirmation_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Application sent') or contains(text(), 'Thank you') or contains(text(), 'submitted')]")
                    
                    if confirmation_elements:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Submission error: {e}")
            return False
    
    def _apply_external(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to external job posting"""
        try:
            # Look for external apply button
            apply_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), 'Apply') or contains(@class, 'apply')]")
            
            if apply_buttons:
                # This would open external site - for now, just log it
                logger.info(f"📋 External application required for {job['title']}")
                return {"success": False, "reason": "External application required"}
            
            return {"success": False, "reason": "No apply button found"}
            
        except Exception as e:
            logger.error(f"❌ External application error: {e}")
            return {"success": False, "error": str(e)}
    
    def run_automation(self, email: str, password: str, max_applications: int = 10) -> Dict[str, Any]:
        """Run the complete automation"""
        try:
            self.max_applications = max_applications
            results = {
                "total_applications": 0,
                "successful_applications": 0,
                "failed_applications": 0,
                "jobs_applied": [],
                "errors": []
            }
            
            # Setup driver
            self.setup_driver()
            
            # Login
            if not self.login_to_linkedin(email, password):
                raise Exception("Failed to login to LinkedIn")
            
            # Search for jobs
            keywords = ["Software Engineer", "Cloud Engineer", "DevOps Engineer", "Python Developer"]
            jobs = self.search_jobs(keywords, "Remote")
            
            if not jobs:
                logger.warning("⚠️ No jobs found")
                return results
            
            # Apply to jobs
            for job in jobs[:max_applications]:
                if self.applications_today >= max_applications:
                    break
                
                # Random delay between applications
                time.sleep(random.randint(30, 60))
                
                application_result = self.apply_to_job(job)
                results["total_applications"] += 1
                
                if application_result.get("success", False):
                    results["successful_applications"] += 1
                    results["jobs_applied"].append({
                        "title": job["title"],
                        "company": job["company"],
                        "status": "success"
                    })
                else:
                    results["failed_applications"] += 1
                    results["errors"].append(f"{job['title']}: {application_result.get('reason', 'Unknown error')}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Automation error: {e}")
            return {"error": str(e)}
        
        finally:
            if self.driver:
                self.driver.quit()

# Test function
def test_linkedin_bot():
    """Test the LinkedIn bot"""
    # Mock user profile
    user_profile = {
        "personal_info": {
            "first_name": "Hemanth Kiran Reddy",
            "last_name": "Polu",
            "email": "<EMAIL>",
            "phone": "(*************"
        },
        "application_answers": {
            "why_interested": "I am passionate about cloud computing and software engineering.",
            "salary_expectation": "$80,000 - $120,000",
            "availability_start_date": "Immediately available"
        },
        "resume_path": "/Users/<USER>/Documents/Projects/test/HemanthKiranReddyPolu_SoftwareEngineer.pdf"
    }
    
    bot = ReliableLinkedInBot(user_profile)
    
    # Test credentials (replace with real ones)
    email = "<EMAIL>"
    password = "your_password"
    
    results = bot.run_automation(email, password, max_applications=3)
    print(f"Results: {results}")

if __name__ == "__main__":
    test_linkedin_bot()
