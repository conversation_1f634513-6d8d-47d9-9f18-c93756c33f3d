"""
Automated job application using browser-use and AI
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import ollama
from browser_use import Agent
from browser_use.browser.browser import Browser
from browser_use.browser.context import BrowserContext
from loguru import logger
from config.settings import settings
from core.user_data import UserDataManager

class JobApplicator:
    """Automated job application system"""
    
    def __init__(self, user_data_manager: UserDataManager):
        self.user_data_manager = user_data_manager
        self.ollama_client = ollama.Client(host=settings.ollama_host)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.applications_today = 0
    
    async def initialize_browser(self):
        """Initialize browser for automation"""
        try:
            self.browser = Browser(
                headless=settings.headless_mode,
                timeout=settings.browser_timeout * 1000
            )
            self.context = await self.browser.new_context()
            logger.info("Job applicator browser initialized")
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def close_browser(self):
        """Close browser and cleanup"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        logger.info("Job applicator browser closed")
    
    async def apply_to_job(self, job_data: Dict[str, Any]) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Apply to a single job
        Returns: (success, status_message, application_details)
        """
        if self.applications_today >= settings.max_applications_per_day:
            return False, "Daily application limit reached", {}
        
        try:
            if not self.context:
                await self.initialize_browser()
            
            page = await self.context.new_page()
            
            # Navigate to job URL
            if not job_data.get('url'):
                return False, "No job URL provided", {}
            
            await page.goto(job_data['url'])
            await page.wait_for_load_state('networkidle')
            
            # Determine application method
            application_method = await self._detect_application_method(page)
            
            if application_method == "Easy Apply":
                success, message = await self._apply_easy_apply(page, job_data)
            elif application_method == "External Site":
                success, message = await self._apply_external_site(page, job_data)
            else:
                success, message = False, "Unknown application method"
            
            if success:
                self.applications_today += 1
                logger.info(f"Successfully applied to {job_data['title']} at {job_data['company']}")
            else:
                logger.warning(f"Failed to apply to {job_data['title']}: {message}")
            
            application_details = {
                'application_method': application_method,
                'applied_at': datetime.now().isoformat(),
                'success': success,
                'message': message
            }
            
            return success, message, application_details
            
        except Exception as e:
            error_msg = f"Error applying to job: {e}"
            logger.error(error_msg)
            return False, error_msg, {}
    
    async def _detect_application_method(self, page) -> str:
        """Detect the application method on the page"""
        try:
            page_content = await page.content()
            
            # Look for Easy Apply buttons
            easy_apply_indicators = [
                "easy apply", "quick apply", "1-click apply",
                "apply now", "apply with linkedin"
            ]
            
            content_lower = page_content.lower()
            for indicator in easy_apply_indicators:
                if indicator in content_lower:
                    return "Easy Apply"
            
            # Check for external application links
            external_indicators = [
                "apply on company website", "visit company site",
                "external application", "company career page"
            ]
            
            for indicator in external_indicators:
                if indicator in content_lower:
                    return "External Site"
            
            return "Unknown"
            
        except Exception as e:
            logger.error(f"Error detecting application method: {e}")
            return "Unknown"
    
    async def _apply_easy_apply(self, page, job_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Handle Easy Apply applications"""
        try:
            # Use browser-use Agent to handle the application
            agent = Agent(
                task=self._create_easy_apply_task(job_data),
                llm=self.ollama_client,
                browser_context=self.context
            )
            
            # Run the application process
            result = await agent.run()
            
            # Check if application was successful
            if "successfully applied" in str(result).lower() or "application submitted" in str(result).lower():
                return True, "Easy Apply completed successfully"
            else:
                return False, "Easy Apply process failed or incomplete"
                
        except Exception as e:
            logger.error(f"Error in Easy Apply process: {e}")
            return False, f"Easy Apply error: {e}"
    
    async def _apply_external_site(self, page, job_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Handle external site applications"""
        try:
            # Use browser-use Agent to navigate and apply on external sites
            agent = Agent(
                task=self._create_external_apply_task(job_data),
                llm=self.ollama_client,
                browser_context=self.context
            )
            
            result = await agent.run()
            
            if "application submitted" in str(result).lower() or "successfully applied" in str(result).lower():
                return True, "External application completed successfully"
            else:
                return False, "External application process failed"
                
        except Exception as e:
            logger.error(f"Error in external application: {e}")
            return False, f"External application error: {e}"
    
    def _create_easy_apply_task(self, job_data: Dict[str, Any]) -> str:
        """Create task description for Easy Apply process"""
        profile = self.user_data_manager.profile
        if not profile:
            return "Apply to this job using Easy Apply"
        
        personal_info = profile.personal_info
        app_answers = profile.application_answers
        
        task = f"""
        Apply to the job "{job_data['title']}" at "{job_data['company']}" using Easy Apply or similar quick application method.
        
        Use this information to fill out the application:
        
        Personal Information:
        - Name: {personal_info.first_name} {personal_info.last_name}
        - Email: {personal_info.email}
        - Phone: {personal_info.phone}
        - Address: {personal_info.address}, {personal_info.city}, {personal_info.state} {personal_info.zip_code}
        
        Application Answers:
        - Why interested: {app_answers.why_interested}
        - Greatest strength: {app_answers.greatest_strength}
        - Career goals: {app_answers.career_goals}
        - Availability: {app_answers.availability_start_date}
        - Work authorization: {app_answers.work_authorization}
        - Willing to relocate: {"Yes" if app_answers.willing_to_relocate else "No"}
        - Preferred work arrangement: {app_answers.preferred_work_arrangement}
        
        Steps:
        1. Look for and click the Easy Apply or Quick Apply button
        2. Fill out all required fields with the provided information
        3. Upload resume if prompted (skip if not available)
        4. Answer any additional questions using the provided answers
        5. Review and submit the application
        6. Confirm the application was submitted successfully
        
        If you encounter any errors or the process fails, note the specific issue.
        """
        
        return task
    
    def _create_external_apply_task(self, job_data: Dict[str, Any]) -> str:
        """Create task description for external site applications"""
        profile = self.user_data_manager.profile
        if not profile:
            return "Navigate to the company's career page and apply to this job"
        
        personal_info = profile.personal_info
        app_answers = profile.application_answers
        
        task = f"""
        Apply to the job "{job_data['title']}" at "{job_data['company']}" on their company website.
        
        Use this information to fill out the application:
        
        Personal Information:
        - Name: {personal_info.first_name} {personal_info.last_name}
        - Email: {personal_info.email}
        - Phone: {personal_info.phone}
        - Address: {personal_info.address}, {personal_info.city}, {personal_info.state} {personal_info.zip_code}
        
        Application Answers:
        - Why interested in this role: {app_answers.why_interested}
        - Greatest strength: {app_answers.greatest_strength}
        - Career goals: {app_answers.career_goals}
        - When can you start: {app_answers.availability_start_date}
        - Work authorization: {app_answers.work_authorization}
        - Willing to relocate: {"Yes" if app_answers.willing_to_relocate else "No"}
        - Preferred work arrangement: {app_answers.preferred_work_arrangement}
        
        Steps:
        1. Look for "Apply" or "Apply Now" button and click it
        2. If redirected to company career page, find the specific job posting
        3. Create an account if required (use the provided email)
        4. Fill out the application form with all provided information
        5. Upload resume if there's an upload option
        6. Answer any additional questions using the provided answers
        7. Submit the application
        8. Look for confirmation that the application was received
        
        Be thorough and patient. If you encounter CAPTCHAs or other verification steps, note them.
        """
        
        return task
    
    async def apply_to_multiple_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply to multiple jobs with rate limiting"""
        results = []
        
        try:
            await self.initialize_browser()
            
            for i, job in enumerate(jobs):
                if self.applications_today >= settings.max_applications_per_day:
                    logger.info(f"Reached daily application limit of {settings.max_applications_per_day}")
                    break
                
                logger.info(f"Applying to job {i+1}/{len(jobs)}: {job['title']} at {job['company']}")
                
                success, message, details = await self.apply_to_job(job)
                
                result = {
                    **job,
                    'application_success': success,
                    'application_message': message,
                    'application_details': details
                }
                
                results.append(result)
                
                # Rate limiting - wait between applications
                if i < len(jobs) - 1:  # Don't wait after the last application
                    wait_time = 30  # 30 seconds between applications
                    logger.info(f"Waiting {wait_time} seconds before next application...")
                    await asyncio.sleep(wait_time)
            
            logger.info(f"Completed application process. Applied to {self.applications_today} jobs today.")
            
        except Exception as e:
            logger.error(f"Error in batch application process: {e}")
        finally:
            await self.close_browser()
        
        return results
    
    def filter_applicable_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter jobs that are suitable for application"""
        applicable_jobs = []
        
        for job in jobs:
            # Skip if already applied
            if job.get('applied', False):
                continue
            
            # Check if job matches criteria
            if self._job_matches_criteria(job):
                applicable_jobs.append(job)
        
        # Sort by priority (you can customize this logic)
        applicable_jobs.sort(key=lambda x: self._calculate_job_priority(x), reverse=True)
        
        return applicable_jobs
    
    def _job_matches_criteria(self, job: Dict[str, Any]) -> bool:
        """Check if job matches user criteria"""
        title = job.get('title', '').lower()
        company = job.get('company', '').lower()
        location = job.get('location', '').lower()
        
        # Check if title contains target roles
        target_roles_lower = [role.lower() for role in settings.target_roles]
        if not any(role in title for role in target_roles_lower):
            return False
        
        # Check location preferences
        target_locations_lower = [loc.lower() for loc in settings.target_locations]
        if not any(loc in location for loc in target_locations_lower):
            return False
        
        return True
    
    def _calculate_job_priority(self, job: Dict[str, Any]) -> int:
        """Calculate priority score for job application"""
        score = 0
        
        title = job.get('title', '').lower()
        company = job.get('company', '').lower()
        
        # Prefer certain keywords in title
        high_priority_keywords = ['senior', 'lead', 'principal', 'staff']
        medium_priority_keywords = ['engineer', 'developer', 'architect']
        
        for keyword in high_priority_keywords:
            if keyword in title:
                score += 10
        
        for keyword in medium_priority_keywords:
            if keyword in title:
                score += 5
        
        # Prefer Easy Apply jobs
        if job.get('application_method') == 'Easy Apply':
            score += 15
        
        # Prefer remote jobs
        location = job.get('location', '').lower()
        if 'remote' in location:
            score += 10
        
        return score

if __name__ == "__main__":
    # Test the job applicator
    async def test_application():
        from core.user_data import UserDataManager
        
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("No user data found. Please run user data setup first.")
            return
        
        applicator = JobApplicator(user_manager)
        
        # Test with a sample job
        test_job = {
            'title': 'Software Engineer',
            'company': 'Test Company',
            'url': 'https://example.com/job',
            'location': 'Remote',
            'application_method': 'Easy Apply'
        }
        
        success, message, details = await applicator.apply_to_job(test_job)
        print(f"Application result: {success}, Message: {message}")
    
    # Uncomment to test
    # asyncio.run(test_application())
