"""
Job search automation using browser-use and Ollama
"""
import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
import ollama
from browser_use import Agent
from browser_use.browser.browser import <PERSON>rowser
from browser_use.browser.context import Browser<PERSON>ontext
from loguru import logger
from config.settings import settings

class JobSearcher:
    """Automated job searching across multiple platforms"""
    
    def __init__(self):
        self.ollama_client = ollama.Client(host=settings.ollama_host)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.found_jobs = []
    
    async def initialize_browser(self):
        """Initialize browser for automation"""
        try:
            self.browser = Browser(
                headless=settings.headless_mode,
                timeout=settings.browser_timeout * 1000  # Convert to milliseconds
            )
            self.context = await self.browser.new_context()
            logger.info("Browser initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def close_browser(self):
        """Close browser and cleanup"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        logger.info("Browser closed")
    
    async def search_linkedin_jobs(self, 
                                  keywords: List[str],
                                  locations: List[str],
                                  experience_levels: List[str],
                                  max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for jobs on LinkedIn"""
        jobs = []
        
        try:
            if not self.context:
                await self.initialize_browser()
            
            page = await self.context.new_page()
            
            # Build LinkedIn search URL
            search_terms = " OR ".join(keywords)
            location_terms = " OR ".join(locations)
            
            # LinkedIn job search URL
            base_url = "https://www.linkedin.com/jobs/search"
            search_params = {
                'keywords': search_terms,
                'location': location_terms,
                'f_E': self._get_linkedin_experience_filter(experience_levels),
                'f_TPR': 'r86400',  # Past 24 hours
                'sortBy': 'DD'  # Most recent
            }
            
            # Construct URL
            url_params = "&".join([f"{k}={v}" for k, v in search_params.items() if v])
            search_url = f"{base_url}?{url_params}"
            
            logger.info(f"Searching LinkedIn: {search_url}")
            
            # Navigate to LinkedIn jobs
            await page.goto(search_url)
            await page.wait_for_load_state('networkidle')
            
            # Use Ollama to help extract job information
            page_content = await page.content()
            
            # Extract job listings using AI
            jobs = await self._extract_jobs_with_ai(page_content, "LinkedIn")
            
            # Limit results
            jobs = jobs[:max_results]
            
            logger.info(f"Found {len(jobs)} jobs on LinkedIn")
            
        except Exception as e:
            logger.error(f"Error searching LinkedIn jobs: {e}")
        
        return jobs
    
    async def search_indeed_jobs(self,
                                keywords: List[str],
                                locations: List[str],
                                max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for jobs on Indeed"""
        jobs = []
        
        try:
            if not self.context:
                await self.initialize_browser()
            
            page = await self.context.new_page()
            
            # Build Indeed search URL
            search_terms = " ".join(keywords)
            location_terms = locations[0] if locations else "Remote"
            
            search_url = f"https://www.indeed.com/jobs?q={search_terms}&l={location_terms}&fromage=1&sort=date"
            
            logger.info(f"Searching Indeed: {search_url}")
            
            await page.goto(search_url)
            await page.wait_for_load_state('networkidle')
            
            page_content = await page.content()
            jobs = await self._extract_jobs_with_ai(page_content, "Indeed")
            
            jobs = jobs[:max_results]
            logger.info(f"Found {len(jobs)} jobs on Indeed")
            
        except Exception as e:
            logger.error(f"Error searching Indeed jobs: {e}")
        
        return jobs
    
    async def search_glassdoor_jobs(self,
                                   keywords: List[str],
                                   locations: List[str],
                                   max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for jobs on Glassdoor"""
        jobs = []
        
        try:
            if not self.context:
                await self.initialize_browser()
            
            page = await self.context.new_page()
            
            search_terms = " ".join(keywords)
            location_terms = locations[0] if locations else "Remote"
            
            search_url = f"https://www.glassdoor.com/Job/jobs.htm?sc.keyword={search_terms}&locT=&locId=&jobType="
            
            logger.info(f"Searching Glassdoor: {search_url}")
            
            await page.goto(search_url)
            await page.wait_for_load_state('networkidle')
            
            page_content = await page.content()
            jobs = await self._extract_jobs_with_ai(page_content, "Glassdoor")
            
            jobs = jobs[:max_results]
            logger.info(f"Found {len(jobs)} jobs on Glassdoor")
            
        except Exception as e:
            logger.error(f"Error searching Glassdoor jobs: {e}")
        
        return jobs
    
    async def _extract_jobs_with_ai(self, page_content: str, platform: str) -> List[Dict[str, Any]]:
        """Use Ollama to extract job information from page content"""
        try:
            # Create a prompt for job extraction
            prompt = f"""
            Extract job listings from this {platform} page content. For each job, extract:
            - Job title
            - Company name
            - Location
            - Job URL (if available)
            - Experience level
            - Salary range (if mentioned)
            - Brief description
            - Application method (Easy Apply, External, etc.)
            
            Return the information in JSON format as a list of job objects.
            Only include software engineering, cloud engineering, or related technical roles.
            
            Page content (truncated):
            {page_content[:8000]}  # Limit content to avoid token limits
            """
            
            response = self.ollama_client.chat(
                model=settings.ollama_model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            # Parse the AI response
            ai_response = response['message']['content']
            
            # Try to extract JSON from the response
            jobs = self._parse_ai_job_response(ai_response, platform)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Error extracting jobs with AI: {e}")
            return []
    
    def _parse_ai_job_response(self, ai_response: str, platform: str) -> List[Dict[str, Any]]:
        """Parse AI response and extract job data"""
        jobs = []
        
        try:
            import json
            
            # Try to find JSON in the response
            json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
            if json_match:
                jobs_data = json.loads(json_match.group())
                
                for job_data in jobs_data:
                    if isinstance(job_data, dict):
                        job = {
                            'title': job_data.get('title', '').strip(),
                            'company': job_data.get('company', '').strip(),
                            'location': job_data.get('location', '').strip(),
                            'url': job_data.get('url', '').strip(),
                            'experience_level': job_data.get('experience_level', '').strip(),
                            'salary_range': job_data.get('salary_range', '').strip(),
                            'description': job_data.get('description', '').strip(),
                            'application_method': job_data.get('application_method', 'External').strip(),
                            'platform': platform,
                            'found_date': datetime.now().isoformat(),
                            'applied': False
                        }
                        
                        # Validate required fields
                        if job['title'] and job['company']:
                            jobs.append(job)
            
        except Exception as e:
            logger.error(f"Error parsing AI job response: {e}")
            
            # Fallback: try to extract basic information with regex
            jobs = self._fallback_job_extraction(ai_response, platform)
        
        return jobs
    
    def _fallback_job_extraction(self, text: str, platform: str) -> List[Dict[str, Any]]:
        """Fallback method to extract jobs using regex patterns"""
        jobs = []
        
        # Simple patterns to extract job information
        job_patterns = [
            r'(?i)(software engineer|cloud engineer|devops|backend|frontend|full stack).*?at\s+([A-Za-z0-9\s&.,]+)',
            r'(?i)([A-Za-z0-9\s&.,]+)\s*-\s*(software engineer|cloud engineer|devops|backend|frontend)',
        ]
        
        for pattern in job_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) == 2:
                    title, company = match
                    job = {
                        'title': title.strip(),
                        'company': company.strip(),
                        'location': 'Unknown',
                        'url': '',
                        'experience_level': 'Unknown',
                        'salary_range': '',
                        'description': '',
                        'application_method': 'External',
                        'platform': platform,
                        'found_date': datetime.now().isoformat(),
                        'applied': False
                    }
                    jobs.append(job)
        
        return jobs[:10]  # Limit fallback results
    
    def _get_linkedin_experience_filter(self, experience_levels: List[str]) -> str:
        """Convert experience levels to LinkedIn filter format"""
        level_mapping = {
            'internship': '1',
            'entry level': '2',
            'associate': '3',
            'mid-senior level': '4',
            'director': '5',
            'executive': '6'
        }
        
        filters = []
        for level in experience_levels:
            level_lower = level.lower()
            if level_lower in level_mapping:
                filters.append(level_mapping[level_lower])
        
        return ','.join(filters) if filters else '1,2'  # Default to internship and entry level
    
    async def search_all_platforms(self) -> List[Dict[str, Any]]:
        """Search all job platforms and return combined results"""
        all_jobs = []
        
        try:
            await self.initialize_browser()
            
            # Search LinkedIn
            linkedin_jobs = await self.search_linkedin_jobs(
                keywords=settings.target_roles,
                locations=settings.target_locations,
                experience_levels=settings.experience_level,
                max_results=20
            )
            all_jobs.extend(linkedin_jobs)
            
            # Search Indeed
            indeed_jobs = await self.search_indeed_jobs(
                keywords=settings.target_roles,
                locations=settings.target_locations,
                max_results=20
            )
            all_jobs.extend(indeed_jobs)
            
            # Search Glassdoor
            glassdoor_jobs = await self.search_glassdoor_jobs(
                keywords=settings.target_roles,
                locations=settings.target_locations,
                max_results=10
            )
            all_jobs.extend(glassdoor_jobs)
            
            # Remove duplicates based on title and company
            unique_jobs = self._remove_duplicate_jobs(all_jobs)
            
            logger.info(f"Found {len(unique_jobs)} unique jobs across all platforms")
            
            return unique_jobs
            
        except Exception as e:
            logger.error(f"Error searching all platforms: {e}")
            return []
        finally:
            await self.close_browser()
    
    def _remove_duplicate_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate jobs based on title and company"""
        seen = set()
        unique_jobs = []
        
        for job in jobs:
            key = (job['title'].lower(), job['company'].lower())
            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)
        
        return unique_jobs

if __name__ == "__main__":
    # Test the job searcher
    async def test_search():
        searcher = JobSearcher()
        jobs = await searcher.search_all_platforms()
        
        print(f"Found {len(jobs)} jobs:")
        for i, job in enumerate(jobs[:5], 1):
            print(f"{i}. {job['title']} at {job['company']} ({job['platform']})")
    
    asyncio.run(test_search())
