#!/usr/bin/env python3
"""
Complete LinkedIn Job Application Automation using browser-use
Actually navigates to Jobs, searches, and applies to positions
"""
import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from browser_use import Agent
    from langchain_ollama import ChatOllama
    BROWSER_USE_AVAILABLE = True
except ImportError:
    print("❌ browser-use not available")
    BROWSER_USE_AVAILABLE = False

class CompleteLinkedInAutomation:
    """Complete LinkedIn automation that actually applies to jobs"""
    
    def __init__(self, user_profile: Dict[str, Any]):
        self.user_profile = user_profile
        self.applications_submitted = []
        
        if BROWSER_USE_AVAILABLE:
            # Configure LLM for better performance
            self.llm = ChatOllama(
                model="qwen2.5:7b",
                base_url="http://localhost:11434",
                temperature=0.1,
                top_p=0.3,
                num_predict=256  # Shorter responses
            )
        else:
            self.llm = None
    
    async def run_complete_job_application_workflow(self, email: str, password: str, max_applications: int = 5) -> Dict[str, Any]:
        """Run the complete job application workflow"""
        if not BROWSER_USE_AVAILABLE:
            return {"error": "browser-use not available"}
        
        try:
            personal_info = self.user_profile.get("personal_info", {})
            app_answers = self.user_profile.get("application_answers", {})
            
            print("🚀 Starting Complete LinkedIn Job Application Workflow")
            print("=" * 60)
            
            # Create a comprehensive task that does the ENTIRE workflow
            task = f"""
COMPLETE LINKEDIN JOB APPLICATION WORKFLOW

CREDENTIALS:
Email: {email}
Password: {password}

PROFILE DATA:
Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
Email: {personal_info.get('email', '')}
Phone: {personal_info.get('phone', '')}
Location: {personal_info.get('city', '')}, {personal_info.get('state', '')}

COMPLETE WORKFLOW - DO ALL STEPS:

STEP 1: LOGIN TO LINKEDIN
1. Go to https://www.linkedin.com/login
2. Enter email: {email}
3. Enter password: {password}
4. Click Sign in
5. Wait for login to complete

STEP 2: NAVIGATE TO JOBS
1. Click on "Jobs" in the main navigation
2. Go to LinkedIn Jobs page
3. Confirm you're on the jobs page

STEP 3: SEARCH FOR JOBS
1. In the jobs search box, enter: "Software Engineer"
2. In the location box, enter: "Remote"
3. Click Search
4. Apply filters:
   - Experience level: "Entry level"
   - Date posted: "Past 24 hours"

STEP 4: APPLY TO JOBS (Repeat for {max_applications} jobs)
For each job in the search results:
1. Click on the job posting
2. Read the job title and company
3. Look for "Easy Apply" button
4. If Easy Apply is available:
   a. Click "Easy Apply"
   b. Fill out the application form:
      - First Name: {personal_info.get('first_name', '')}
      - Last Name: {personal_info.get('last_name', '')}
      - Email: {personal_info.get('email', '')}
      - Phone: {personal_info.get('phone', '')}
   c. Answer application questions:
      - Work Authorization: "Authorized to work in the United States"
      - Start Date: "Immediately available"
      - Salary Expectation: "$80,000 - $120,000"
      - Why interested: "{app_answers.get('why_interested', 'Passionate about software engineering')}"
   d. Click "Submit application" or "Send application"
   e. Confirm application was submitted
5. Go back to job search results
6. Move to next job

STEP 5: TRACK RESULTS
Keep track of:
- Job titles and companies applied to
- Application submission confirmations
- Any errors or issues

IMPORTANT INSTRUCTIONS:
- Complete ALL steps in order
- Actually click buttons and fill forms
- Don't stop at the LinkedIn feed - go to Jobs page
- Search for actual jobs and apply to them
- Fill out application forms completely
- Submit applications and confirm submission
- Apply to {max_applications} jobs total

START NOW and complete the entire workflow.
"""
            
            print("🤖 Running browser-use agent for complete workflow...")
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Parse the results
            applications = self._parse_application_results(result)
            
            print(f"✅ Workflow completed. Found {len(applications)} applications.")
            
            return {
                "total_applications": len(applications),
                "successful_applications": len([app for app in applications if app.get("success", False)]),
                "applications": applications,
                "workflow_completed": True,
                "raw_result": str(result)[:1000]  # First 1000 chars
            }
            
        except Exception as e:
            print(f"❌ Workflow error: {e}")
            return {"error": str(e)}
    
    async def apply_to_specific_job_url(self, job_url: str, email: str, password: str) -> Dict[str, Any]:
        """Apply to a specific job URL"""
        if not BROWSER_USE_AVAILABLE:
            return {"error": "browser-use not available"}
        
        try:
            personal_info = self.user_profile.get("personal_info", {})
            app_answers = self.user_profile.get("application_answers", {})
            
            print(f"🎯 Applying to specific job: {job_url}")
            
            task = f"""
APPLY TO SPECIFIC LINKEDIN JOB

CREDENTIALS:
Email: {email}
Password: {password}

JOB URL: {job_url}

PROFILE DATA:
Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
Email: {personal_info.get('email', '')}
Phone: {personal_info.get('phone', '')}

COMPLETE APPLICATION PROCESS:

STEP 1: LOGIN (if needed)
1. If not logged in, go to https://www.linkedin.com/login
2. Enter email: {email}
3. Enter password: {password}
4. Click Sign in

STEP 2: NAVIGATE TO JOB
1. Go to job URL: {job_url}
2. Read the job title and company name
3. Review job description

STEP 3: APPLY TO JOB
1. Look for "Easy Apply" button
2. Click "Easy Apply"
3. Fill out application form step by step:
   - Personal Information:
     * First Name: {personal_info.get('first_name', '')}
     * Last Name: {personal_info.get('last_name', '')}
     * Email: {personal_info.get('email', '')}
     * Phone: {personal_info.get('phone', '')}
   - Answer questions:
     * Work Authorization: "Authorized to work in the United States"
     * Start Date: "Immediately available"
     * Salary: "$80,000 - $120,000"
     * Why interested: "{app_answers.get('why_interested', 'I am passionate about software engineering')}"
4. Upload resume if prompted (skip if not available)
5. Review application
6. Click "Submit application" or "Send application"
7. Confirm application submission

STEP 4: VERIFY SUCCESS
1. Look for confirmation message
2. Note any success indicators
3. Report application status

IMPORTANT:
- Actually complete the application process
- Fill out all required fields
- Submit the application
- Confirm submission was successful

Execute this complete process now.
"""
            
            agent = Agent(task=task, llm=self.llm)
            result = await agent.run()
            
            # Check if application was successful
            result_str = str(result).lower()
            success = any(indicator in result_str for indicator in [
                'application submitted', 'successfully applied', 'application sent',
                'thank you for applying', 'application complete', 'submitted successfully'
            ])
            
            return {
                "success": success,
                "job_url": job_url,
                "applied_at": datetime.now().isoformat(),
                "result_summary": str(result)[:500]
            }
            
        except Exception as e:
            print(f"❌ Specific job application error: {e}")
            return {"error": str(e), "success": False}
    
    def _parse_application_results(self, result: Any) -> List[Dict[str, Any]]:
        """Parse application results from browser-use output"""
        applications = []
        result_str = str(result)
        
        try:
            # Look for application patterns
            patterns = [
                r'applied to ([^,\n]+) at ([^,\n]+)',
                r'application submitted for ([^,\n]+) - ([^,\n]+)',
                r'successfully applied: ([^,\n]+) \| ([^,\n]+)',
                r'job application: ([^,\n]+) at ([^,\n]+)'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, result_str, re.IGNORECASE)
                for match in matches:
                    if len(match) == 2:
                        title, company = match
                        applications.append({
                            "title": title.strip(),
                            "company": company.strip(),
                            "success": True,
                            "applied_at": datetime.now().isoformat(),
                            "method": "Easy Apply"
                        })
            
            # Look for general application indicators
            application_indicators = [
                'application submitted', 'successfully applied', 'easy apply completed',
                'application sent', 'thank you for applying'
            ]
            
            if any(indicator in result_str.lower() for indicator in application_indicators):
                if not applications:  # If no specific jobs found but applications mentioned
                    applications.append({
                        "title": "Software Engineering Position",
                        "company": "LinkedIn Job Search",
                        "success": True,
                        "applied_at": datetime.now().isoformat(),
                        "method": "Easy Apply",
                        "note": "Application completed via browser automation"
                    })
            
        except Exception as e:
            print(f"❌ Error parsing results: {e}")
        
        return applications

# Main execution function
async def run_linkedin_job_automation():
    """Run the complete LinkedIn job automation"""
    print("🤖 Complete LinkedIn Job Application Automation")
    print("=" * 70)
    print("✅ Uses browser-use for intelligent automation")
    print("✅ Actually navigates to Jobs page and applies")
    print("✅ Completes full application workflow")
    print("✅ Fills forms and submits applications")
    print("=" * 70)
    
    if not BROWSER_USE_AVAILABLE:
        print("❌ browser-use not available")
        return
    
    try:
        # Load user profile
        from core.user_data import UserDataManager
        
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return
        
        profile = user_manager.profile
        personal_info = profile.personal_info
        
        print(f"✅ Profile loaded: {personal_info.first_name} {personal_info.last_name}")
        
        # Convert profile to dict
        user_profile_dict = {
            "personal_info": {
                "first_name": personal_info.first_name,
                "last_name": personal_info.last_name,
                "email": personal_info.email,
                "phone": personal_info.phone,
                "city": personal_info.city,
                "state": personal_info.state
            },
            "application_answers": {
                "why_interested": profile.application_answers.why_interested,
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "salary_expectation": profile.application_answers.salary_expectation
            }
        }
        
        # Get credentials
        from getpass import getpass
        
        email = input("LinkedIn Email: ").strip()
        password = getpass("LinkedIn Password: ").strip()
        
        if not email or not password:
            print("❌ Credentials required")
            return
        
        # Get settings
        mode = input("Mode (1=Complete automation, 2=Specific job URL): ").strip()
        
        automation = CompleteLinkedInAutomation(user_profile_dict)
        
        if mode == "2":
            # Apply to specific job
            job_url = input("Enter LinkedIn job URL: ").strip()
            if job_url:
                result = await automation.apply_to_specific_job_url(job_url, email, password)
                
                print("\n📊 Application Results:")
                print("=" * 40)
                if result.get("success", False):
                    print("✅ Application submitted successfully!")
                else:
                    print("❌ Application failed")
                print(f"Details: {result}")
        else:
            # Complete automation
            max_apps = input("Maximum applications (default: 3): ").strip()
            max_apps = int(max_apps) if max_apps.isdigit() else 3
            
            print(f"\n🚀 Starting complete automation for {max_apps} applications...")
            
            results = await automation.run_complete_job_application_workflow(email, password, max_apps)
            
            print("\n📊 Automation Results:")
            print("=" * 40)
            if "error" in results:
                print(f"❌ Error: {results['error']}")
            else:
                print(f"✅ Workflow completed: {results.get('workflow_completed', False)}")
                print(f"📝 Total applications: {results.get('total_applications', 0)}")
                print(f"✅ Successful: {results.get('successful_applications', 0)}")
                
                applications = results.get('applications', [])
                if applications:
                    print("\n📋 Applications submitted:")
                    for app in applications:
                        print(f"  • {app.get('title', 'Unknown')} at {app.get('company', 'Unknown')}")
        
        print("\n🎉 LinkedIn automation completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(run_linkedin_job_automation())
