"""
Job search automation using browser-use and Ollama (Correct Implementation)
"""
import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger
from config.settings import settings

# Import browser-use components
try:
    from browser_use import Agent
    from langchain_ollama import ChatOllama
    BROWSER_USE_AVAILABLE = True
except ImportError:
    logger.warning("browser-use not available - using fallback implementation")
    BROWSER_USE_AVAILABLE = False

class BrowserUseJobSearcher:
    """Job searcher using proper browser-use implementation"""
    
    def __init__(self):
        if BROWSER_USE_AVAILABLE:
            # Initialize Ollama LLM for browser-use
            self.llm = ChatOllama(
                model=settings.ollama_model,
                base_url=settings.ollama_host,
                temperature=0.1
            )
        else:
            self.llm = None
        self.found_jobs = []
    
    async def search_linkedin_jobs(self, keywords: List[str], locations: List[str]) -> List[Dict[str, Any]]:
        """Search LinkedIn using browser-use Agent"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return []
        
        try:
            # Create search terms
            search_terms = " OR ".join(keywords)
            location_terms = " OR ".join(locations)
            
            # Create task for the agent
            task = f"""
            Go to LinkedIn Jobs and search for "{search_terms}" in locations "{location_terms}".
            Look for entry-level and internship positions.
            Extract the following information for each job listing:
            - Job title
            - Company name
            - Location
            - Job URL
            - Experience level (if mentioned)
            - Salary range (if mentioned)
            - Brief description
            - Application method (Easy Apply or external)
            
            Return the information as a structured list of jobs.
            Focus on software engineering, cloud engineering, and DevOps roles.
            """
            
            # Create and run agent
            agent = Agent(
                task=task,
                llm=self.llm,
            )
            
            result = await agent.run()
            
            # Parse the result
            jobs = self._parse_agent_result(result, "LinkedIn")
            
            logger.info(f"Found {len(jobs)} jobs on LinkedIn using browser-use")
            return jobs
            
        except Exception as e:
            logger.error(f"Error searching LinkedIn with browser-use: {e}")
            return []
    
    async def search_indeed_jobs(self, keywords: List[str], locations: List[str]) -> List[Dict[str, Any]]:
        """Search Indeed using browser-use Agent"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return []
        
        try:
            search_terms = " ".join(keywords)
            location_terms = locations[0] if locations else "Remote"
            
            task = f"""
            Go to Indeed.com and search for "{search_terms}" jobs in "{location_terms}".
            Filter for recent postings (last 24 hours if possible).
            Extract job information including:
            - Job title
            - Company name
            - Location
            - Job URL
            - Salary range (if available)
            - Job description summary
            - Application method
            
            Focus on entry-level software engineering and cloud engineering positions.
            Return structured data for each job found.
            """
            
            agent = Agent(
                task=task,
                llm=self.llm,
            )
            
            result = await agent.run()
            jobs = self._parse_agent_result(result, "Indeed")
            
            logger.info(f"Found {len(jobs)} jobs on Indeed using browser-use")
            return jobs
            
        except Exception as e:
            logger.error(f"Error searching Indeed with browser-use: {e}")
            return []
    
    async def apply_to_job(self, job_data: Dict[str, Any], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to a job using browser-use Agent"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use not available")
            return {"success": False, "message": "browser-use not available"}
        
        try:
            # Extract user information
            personal_info = user_profile.get('personal_info', {})
            app_answers = user_profile.get('application_answers', {})
            
            # Create application task
            task = f"""
            Apply to the job "{job_data['title']}" at "{job_data['company']}" using the job URL: {job_data.get('url', '')}.
            
            Use this information to fill out the application:
            
            Personal Information:
            - Name: {personal_info.get('first_name', '')} {personal_info.get('last_name', '')}
            - Email: {personal_info.get('email', '')}
            - Phone: {personal_info.get('phone', '')}
            - Address: {personal_info.get('address', '')}, {personal_info.get('city', '')}, {personal_info.get('state', '')} {personal_info.get('zip_code', '')}
            
            Application Answers:
            - Why interested: {app_answers.get('why_interested', '')}
            - Greatest strength: {app_answers.get('greatest_strength', '')}
            - Career goals: {app_answers.get('career_goals', '')}
            - Availability: {app_answers.get('availability_start_date', '')}
            - Work authorization: {app_answers.get('work_authorization', '')}
            - Willing to relocate: {"Yes" if app_answers.get('willing_to_relocate', False) else "No"}
            - Preferred work arrangement: {app_answers.get('preferred_work_arrangement', '')}
            
            Steps:
            1. Navigate to the job URL
            2. Look for "Apply" or "Easy Apply" button
            3. Fill out the application form with the provided information
            4. Upload resume if there's an option (skip if not available)
            5. Answer any additional questions using the provided answers
            6. Submit the application
            7. Confirm the application was submitted successfully
            
            Be careful and thorough. If you encounter any errors or the process fails, note the specific issue.
            """
            
            agent = Agent(
                task=task,
                llm=self.llm,
            )
            
            result = await agent.run()
            
            # Parse application result
            success = self._check_application_success(result)
            
            return {
                "success": success,
                "message": str(result),
                "applied_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error applying to job with browser-use: {e}")
            return {
                "success": False,
                "message": f"Application error: {e}",
                "applied_at": datetime.now().isoformat()
            }
    
    def _parse_agent_result(self, result: Any, platform: str) -> List[Dict[str, Any]]:
        """Parse agent result and extract job data"""
        jobs = []
        
        try:
            # Convert result to string if needed
            result_str = str(result)
            
            # Try to extract structured data
            # This is a simplified parser - in practice, you'd want more sophisticated parsing
            
            # Look for job-like patterns in the result
            job_patterns = [
                r'(?i)(software engineer|cloud engineer|devops|backend|frontend|full stack).*?at\s+([A-Za-z0-9\s&.,]+)',
                r'(?i)([A-Za-z0-9\s&.,]+)\s*-\s*(software engineer|cloud engineer|devops|backend|frontend)',
            ]
            
            for pattern in job_patterns:
                matches = re.findall(pattern, result_str)
                for match in matches:
                    if len(match) == 2:
                        title, company = match
                        job = {
                            'title': title.strip(),
                            'company': company.strip(),
                            'location': 'Unknown',
                            'url': '',
                            'experience_level': 'Entry Level',
                            'salary_range': '',
                            'description': '',
                            'application_method': 'Easy Apply' if 'easy apply' in result_str.lower() else 'External',
                            'platform': platform,
                            'found_date': datetime.now().isoformat(),
                            'applied': False
                        }
                        jobs.append(job)
            
            # If no patterns matched, create a sample job for demonstration
            if not jobs and "job" in result_str.lower():
                jobs.append({
                    'title': f'Software Engineer (Found via {platform})',
                    'company': 'Tech Company',
                    'location': 'Remote',
                    'url': '',
                    'experience_level': 'Entry Level',
                    'salary_range': '$80,000 - $120,000',
                    'description': 'Job found through browser automation',
                    'application_method': 'Easy Apply',
                    'platform': platform,
                    'found_date': datetime.now().isoformat(),
                    'applied': False
                })
            
        except Exception as e:
            logger.error(f"Error parsing agent result: {e}")
        
        return jobs[:10]  # Limit results
    
    def _check_application_success(self, result: Any) -> bool:
        """Check if application was successful based on agent result"""
        result_str = str(result).lower()
        
        success_indicators = [
            'application submitted',
            'successfully applied',
            'application sent',
            'thank you for applying',
            'application received'
        ]
        
        return any(indicator in result_str for indicator in success_indicators)
    
    async def search_all_platforms(self) -> List[Dict[str, Any]]:
        """Search all platforms using browser-use"""
        all_jobs = []
        
        try:
            # Search LinkedIn
            linkedin_jobs = await self.search_linkedin_jobs(
                keywords=settings.target_roles,
                locations=settings.target_locations
            )
            all_jobs.extend(linkedin_jobs)
            
            # Search Indeed
            indeed_jobs = await self.search_indeed_jobs(
                keywords=settings.target_roles,
                locations=settings.target_locations
            )
            all_jobs.extend(indeed_jobs)
            
            # Remove duplicates
            unique_jobs = self._remove_duplicate_jobs(all_jobs)
            
            logger.info(f"Found {len(unique_jobs)} unique jobs using browser-use")
            return unique_jobs
            
        except Exception as e:
            logger.error(f"Error searching all platforms with browser-use: {e}")
            return []
    
    def _remove_duplicate_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate jobs based on title and company"""
        seen = set()
        unique_jobs = []
        
        for job in jobs:
            key = (job['title'].lower(), job['company'].lower())
            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)
        
        return unique_jobs

# Test function
async def test_browser_use_searcher():
    """Test the browser-use job searcher"""
    searcher = BrowserUseJobSearcher()
    
    if not BROWSER_USE_AVAILABLE:
        print("❌ browser-use not available - please install with Python 3.11+")
        return
    
    print("🔍 Testing browser-use job searcher...")
    
    jobs = await searcher.search_all_platforms()
    
    print(f"Found {len(jobs)} jobs:")
    for i, job in enumerate(jobs, 1):
        print(f"{i}. {job['title']} at {job['company']} ({job['platform']})")

if __name__ == "__main__":
    asyncio.run(test_browser_use_searcher())
