"""
User data collection and management system
"""
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from pydantic import BaseModel, validator, EmailStr
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.table import Table
from utils.security import SecureDataManager
from config.settings import settings
from loguru import logger

console = Console()

class PersonalInfo(BaseModel):
    """Personal information model"""
    first_name: str
    last_name: str
    email: EmailStr
    phone: str
    address: str
    city: str
    state: str
    zip_code: str
    country: str = "United States"
    linkedin_url: Optional[str] = None
    github_url: Optional[str] = None
    portfolio_url: Optional[str] = None

class WorkExperience(BaseModel):
    """Work experience model"""
    company: str
    position: str
    start_date: str
    end_date: Optional[str] = None
    description: str
    technologies: List[str] = []
    is_current: bool = False

class Education(BaseModel):
    """Education model"""
    institution: str
    degree: str
    field_of_study: str
    graduation_date: str
    gpa: Optional[float] = None

class ApplicationAnswers(BaseModel):
    """Common application question answers"""
    why_interested: str
    greatest_strength: str
    biggest_weakness: str
    career_goals: str
    why_leaving_current_job: Optional[str] = None
    salary_expectation: Optional[str] = None
    availability_start_date: str
    work_authorization: str
    willing_to_relocate: bool
    preferred_work_arrangement: str  # Remote, Hybrid, On-site

class UserProfile(BaseModel):
    """Complete user profile"""
    personal_info: PersonalInfo
    work_experience: List[WorkExperience]
    education: List[Education]
    skills: List[str]
    certifications: List[str]
    application_answers: ApplicationAnswers
    resume_path: Optional[str] = None
    cover_letter_template: Optional[str] = None
    created_at: datetime
    updated_at: datetime

class UserDataManager:
    """Manages user data collection, storage, and retrieval"""
    
    def __init__(self):
        self.security_manager = SecureDataManager(
            encryption_key=settings.encryption_key,
            storage_path=settings.data_storage_path
        )
        self.profile: Optional[UserProfile] = None
    
    def load_user_data(self) -> bool:
        """Load existing user data"""
        try:
            data = self.security_manager.decrypt_data()
            if data:
                self.profile = UserProfile(**data)
                logger.info("User data loaded successfully")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to load user data: {e}")
            return False
    
    def save_user_data(self) -> bool:
        """Save user data securely"""
        if not self.profile:
            logger.error("No profile data to save")
            return False
        
        try:
            self.profile.updated_at = datetime.now()
            data = self.profile.dict()
            return self.security_manager.encrypt_data(data)
        except Exception as e:
            logger.error(f"Failed to save user data: {e}")
            return False
    
    def collect_initial_data(self) -> bool:
        """Interactive data collection for first-time users"""
        console.print("\n[bold blue]Welcome to the Job Application Agent![/bold blue]")
        console.print("Let's collect your information to get started.\n")
        
        try:
            # Personal Information
            personal_info = self._collect_personal_info()
            
            # Work Experience
            work_experience = self._collect_work_experience()
            
            # Education
            education = self._collect_education()
            
            # Skills and Certifications
            skills = self._collect_skills()
            certifications = self._collect_certifications()
            
            # Application Answers
            application_answers = self._collect_application_answers()
            
            # Create profile
            self.profile = UserProfile(
                personal_info=personal_info,
                work_experience=work_experience,
                education=education,
                skills=skills,
                certifications=certifications,
                application_answers=application_answers,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Save data
            if self.save_user_data():
                console.print("\n[bold green]✓ Profile created and saved successfully![/bold green]")
                return True
            else:
                console.print("\n[bold red]✗ Failed to save profile data[/bold red]")
                return False
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Setup cancelled by user[/yellow]")
            return False
        except Exception as e:
            logger.error(f"Error during data collection: {e}")
            console.print(f"\n[bold red]Error: {e}[/bold red]")
            return False
    
    def _collect_personal_info(self) -> PersonalInfo:
        """Collect personal information"""
        console.print("[bold]Personal Information[/bold]")
        
        return PersonalInfo(
            first_name=Prompt.ask("First Name"),
            last_name=Prompt.ask("Last Name"),
            email=Prompt.ask("Email Address"),
            phone=Prompt.ask("Phone Number"),
            address=Prompt.ask("Street Address"),
            city=Prompt.ask("City"),
            state=Prompt.ask("State/Province"),
            zip_code=Prompt.ask("ZIP/Postal Code"),
            country=Prompt.ask("Country", default="United States"),
            linkedin_url=Prompt.ask("LinkedIn URL (optional)", default=""),
            github_url=Prompt.ask("GitHub URL (optional)", default=""),
            portfolio_url=Prompt.ask("Portfolio URL (optional)", default="")
        )
    
    def _collect_work_experience(self) -> List[WorkExperience]:
        """Collect work experience"""
        console.print("\n[bold]Work Experience[/bold]")
        experiences = []
        
        while True:
            if experiences:
                if not Confirm.ask("Add another work experience?"):
                    break
            
            console.print(f"\n[cyan]Experience #{len(experiences) + 1}[/cyan]")
            
            company = Prompt.ask("Company Name")
            position = Prompt.ask("Position/Title")
            start_date = Prompt.ask("Start Date (MM/YYYY)")
            is_current = Confirm.ask("Is this your current position?")
            end_date = None if is_current else Prompt.ask("End Date (MM/YYYY)")
            description = Prompt.ask("Job Description/Responsibilities")
            
            tech_input = Prompt.ask("Technologies used (comma-separated)", default="")
            technologies = [tech.strip() for tech in tech_input.split(",") if tech.strip()]
            
            experiences.append(WorkExperience(
                company=company,
                position=position,
                start_date=start_date,
                end_date=end_date,
                description=description,
                technologies=technologies,
                is_current=is_current
            ))
        
        return experiences
    
    def _collect_education(self) -> List[Education]:
        """Collect education information"""
        console.print("\n[bold]Education[/bold]")
        education_list = []
        
        while True:
            if education_list:
                if not Confirm.ask("Add another education entry?"):
                    break
            
            console.print(f"\n[cyan]Education #{len(education_list) + 1}[/cyan]")
            
            institution = Prompt.ask("Institution Name")
            degree = Prompt.ask("Degree Type (e.g., Bachelor's, Master's)")
            field_of_study = Prompt.ask("Field of Study/Major")
            graduation_date = Prompt.ask("Graduation Date (MM/YYYY)")
            
            gpa_input = Prompt.ask("GPA (optional)", default="")
            gpa = float(gpa_input) if gpa_input else None
            
            education_list.append(Education(
                institution=institution,
                degree=degree,
                field_of_study=field_of_study,
                graduation_date=graduation_date,
                gpa=gpa
            ))
        
        return education_list
    
    def _collect_skills(self) -> List[str]:
        """Collect skills"""
        console.print("\n[bold]Skills[/bold]")
        skills_input = Prompt.ask("Enter your skills (comma-separated)")
        return [skill.strip() for skill in skills_input.split(",") if skill.strip()]
    
    def _collect_certifications(self) -> List[str]:
        """Collect certifications"""
        console.print("\n[bold]Certifications[/bold]")
        cert_input = Prompt.ask("Enter your certifications (comma-separated, optional)", default="")
        return [cert.strip() for cert in cert_input.split(",") if cert.strip()]

    def _collect_application_answers(self) -> ApplicationAnswers:
        """Collect common application question answers"""
        console.print("\n[bold]Application Questions[/bold]")
        console.print("These answers will be used to automatically fill application forms.")

        return ApplicationAnswers(
            why_interested=Prompt.ask("Why are you interested in software/cloud engineering?"),
            greatest_strength=Prompt.ask("What is your greatest professional strength?"),
            biggest_weakness=Prompt.ask("What is your biggest weakness?"),
            career_goals=Prompt.ask("What are your career goals?"),
            why_leaving_current_job=Prompt.ask("Why are you leaving your current job? (optional)", default=""),
            salary_expectation=Prompt.ask("Salary expectation (optional)", default=""),
            availability_start_date=Prompt.ask("When can you start? (e.g., Immediately, 2 weeks notice)"),
            work_authorization=Prompt.ask("Work authorization status", default="Authorized to work in the US"),
            willing_to_relocate=Confirm.ask("Are you willing to relocate?"),
            preferred_work_arrangement=Prompt.ask("Preferred work arrangement",
                                                 choices=["Remote", "Hybrid", "On-site"],
                                                 default="Remote")
        )

    def display_profile_summary(self):
        """Display a summary of the user profile"""
        if not self.profile:
            console.print("[red]No profile data available[/red]")
            return

        console.print("\n[bold blue]Profile Summary[/bold blue]")

        # Personal Info Table
        personal_table = Table(title="Personal Information")
        personal_table.add_column("Field", style="cyan")
        personal_table.add_column("Value", style="white")

        pi = self.profile.personal_info
        personal_table.add_row("Name", f"{pi.first_name} {pi.last_name}")
        personal_table.add_row("Email", pi.email)
        personal_table.add_row("Phone", pi.phone)
        personal_table.add_row("Location", f"{pi.city}, {pi.state}")

        console.print(personal_table)

        # Work Experience
        if self.profile.work_experience:
            console.print(f"\n[bold]Work Experience:[/bold] {len(self.profile.work_experience)} entries")
            for i, exp in enumerate(self.profile.work_experience, 1):
                console.print(f"  {i}. {exp.position} at {exp.company}")

        # Education
        if self.profile.education:
            console.print(f"\n[bold]Education:[/bold] {len(self.profile.education)} entries")
            for i, edu in enumerate(self.profile.education, 1):
                console.print(f"  {i}. {edu.degree} in {edu.field_of_study} from {edu.institution}")

        # Skills
        console.print(f"\n[bold]Skills:[/bold] {', '.join(self.profile.skills[:10])}")
        if len(self.profile.skills) > 10:
            console.print(f"  ... and {len(self.profile.skills) - 10} more")

    def update_field(self, field_path: str, new_value: Any) -> bool:
        """Update a specific field in the profile"""
        if not self.profile:
            return False

        try:
            # Simple field update logic - can be expanded
            if field_path == "personal_info.email":
                self.profile.personal_info.email = new_value
            elif field_path == "personal_info.phone":
                self.profile.personal_info.phone = new_value
            # Add more field paths as needed

            return self.save_user_data()
        except Exception as e:
            logger.error(f"Failed to update field {field_path}: {e}")
            return False

    def check_missing_fields(self) -> List[str]:
        """Check for missing or incomplete profile fields"""
        missing_fields = []

        if not self.profile:
            return ["Complete profile setup required"]

        # Check resume
        if not self.profile.resume_path or not Path(self.profile.resume_path).exists():
            missing_fields.append("Resume file")

        # Check Notion credentials
        if not settings.notion_api_key or not settings.notion_database_id:
            missing_fields.append("Notion API credentials")

        # Check email credentials
        if not settings.email_address or not settings.email_password:
            missing_fields.append("Email credentials for notifications")

        return missing_fields

if __name__ == "__main__":
    # Test the user data manager
    manager = UserDataManager()

    if not manager.load_user_data():
        console.print("No existing profile found. Starting setup...")
        if manager.collect_initial_data():
            console.print("Setup completed successfully!")
        else:
            console.print("Setup failed or cancelled.")
    else:
        console.print("Profile loaded successfully!")
        manager.display_profile_summary()
