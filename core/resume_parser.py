"""
Resume PDF parsing and text extraction
"""
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import PyPDF2
import pdfplumber
from loguru import logger

class ResumeParser:
    """Extracts and parses information from resume PDFs"""
    
    def __init__(self):
        self.text_content = ""
        self.parsed_data = {}
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF using multiple methods for better accuracy"""
        pdf_path = Path(pdf_path)
        
        if not pdf_path.exists():
            logger.error(f"Resume file not found: {pdf_path}")
            return ""
        
        text = ""
        
        # Method 1: Try pdfplumber first (better for complex layouts)
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            if text.strip():
                logger.info("Successfully extracted text using pdfplumber")
                self.text_content = text
                return text
        except Exception as e:
            logger.warning(f"pdfplumber extraction failed: {e}")
        
        # Method 2: Fallback to PyPDF2
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            
            if text.strip():
                logger.info("Successfully extracted text using PyPDF2")
                self.text_content = text
                return text
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {e}")
        
        logger.error("Failed to extract text from PDF")
        return ""
    
    def parse_contact_info(self, text: str) -> Dict[str, str]:
        """Extract contact information from resume text"""
        contact_info = {}
        
        # Email extraction
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            contact_info['email'] = emails[0]
        
        # Phone number extraction
        phone_patterns = [
            r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # (************* or ************
            r'\+?1?[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # ****** 456 7890
        ]
        
        for pattern in phone_patterns:
            phones = re.findall(pattern, text)
            if phones:
                contact_info['phone'] = phones[0]
                break
        
        # LinkedIn URL extraction
        linkedin_pattern = r'linkedin\.com/in/[\w-]+'
        linkedin_matches = re.findall(linkedin_pattern, text, re.IGNORECASE)
        if linkedin_matches:
            contact_info['linkedin'] = f"https://{linkedin_matches[0]}"
        
        # GitHub URL extraction
        github_pattern = r'github\.com/[\w-]+'
        github_matches = re.findall(github_pattern, text, re.IGNORECASE)
        if github_matches:
            contact_info['github'] = f"https://{github_matches[0]}"
        
        return contact_info
    
    def parse_skills(self, text: str) -> List[str]:
        """Extract skills from resume text"""
        # Common technical skills keywords
        skill_keywords = [
            # Programming Languages
            'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust',
            'php', 'ruby', 'swift', 'kotlin', 'scala', 'r', 'matlab',
            
            # Web Technologies
            'html', 'css', 'react', 'angular', 'vue', 'node.js', 'express',
            'django', 'flask', 'spring', 'laravel', 'rails',
            
            # Databases
            'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
            'sqlite', 'oracle', 'sql server', 'dynamodb',
            
            # Cloud & DevOps
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform',
            'jenkins', 'gitlab', 'github actions', 'ansible', 'chef', 'puppet',
            
            # Tools & Frameworks
            'git', 'linux', 'bash', 'powershell', 'vim', 'vscode',
            'jira', 'confluence', 'slack', 'teams',
            
            # Data & Analytics
            'pandas', 'numpy', 'scikit-learn', 'tensorflow', 'pytorch',
            'spark', 'hadoop', 'kafka', 'airflow',
        ]
        
        found_skills = []
        text_lower = text.lower()
        
        for skill in skill_keywords:
            if skill.lower() in text_lower:
                found_skills.append(skill.title())
        
        # Remove duplicates and sort
        return sorted(list(set(found_skills)))
    
    def parse_experience_years(self, text: str) -> Optional[int]:
        """Estimate years of experience from resume"""
        # Look for patterns like "3 years of experience", "5+ years", etc.
        experience_patterns = [
            r'(\d+)\+?\s*years?\s*of\s*experience',
            r'(\d+)\+?\s*years?\s*experience',
            r'experience:\s*(\d+)\+?\s*years?',
        ]
        
        for pattern in experience_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return int(matches[0])
        
        # Fallback: count date ranges in experience section
        date_pattern = r'(20\d{2})\s*[-–]\s*(20\d{2}|present|current)'
        date_ranges = re.findall(date_pattern, text, re.IGNORECASE)
        
        if date_ranges:
            total_years = 0
            current_year = 2024  # Update as needed
            
            for start_year, end_year in date_ranges:
                start = int(start_year)
                end = current_year if end_year.lower() in ['present', 'current'] else int(end_year)
                total_years += max(0, end - start)
            
            return total_years
        
        return None
    
    def parse_education(self, text: str) -> List[Dict[str, str]]:
        """Extract education information"""
        education = []
        
        # Common degree patterns
        degree_patterns = [
            r'(bachelor[\'s]*|b\.?[as]\.?|bs|ba)\s+(?:of\s+)?(?:science\s+)?(?:in\s+)?([^\n,]+)',
            r'(master[\'s]*|m\.?[as]\.?|ms|ma|mba)\s+(?:of\s+)?(?:science\s+)?(?:in\s+)?([^\n,]+)',
            r'(phd|ph\.?d\.?|doctorate)\s+(?:in\s+)?([^\n,]+)',
        ]
        
        for pattern in degree_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for degree_type, field in matches:
                education.append({
                    'degree': degree_type.title(),
                    'field': field.strip()
                })
        
        return education
    
    def parse_full_resume(self, pdf_path: str) -> Dict[str, Any]:
        """Parse complete resume and return structured data"""
        text = self.extract_text_from_pdf(pdf_path)
        
        if not text:
            return {}
        
        parsed_data = {
            'contact_info': self.parse_contact_info(text),
            'skills': self.parse_skills(text),
            'experience_years': self.parse_experience_years(text),
            'education': self.parse_education(text),
            'raw_text': text,
            'text_length': len(text),
            'parsed_at': str(Path(pdf_path).stat().st_mtime)
        }
        
        self.parsed_data = parsed_data
        logger.info(f"Resume parsed successfully. Found {len(parsed_data['skills'])} skills")
        
        return parsed_data
    
    def get_summary_for_applications(self) -> str:
        """Generate a concise summary for job applications"""
        if not self.parsed_data:
            return ""
        
        skills = self.parsed_data.get('skills', [])
        experience_years = self.parsed_data.get('experience_years')
        education = self.parsed_data.get('education', [])
        
        summary_parts = []
        
        if experience_years:
            summary_parts.append(f"{experience_years} years of experience")
        
        if skills:
            top_skills = skills[:8]  # Top 8 skills
            summary_parts.append(f"Skills: {', '.join(top_skills)}")
        
        if education:
            degrees = [f"{edu['degree']} in {edu['field']}" for edu in education]
            summary_parts.append(f"Education: {'; '.join(degrees)}")
        
        return ". ".join(summary_parts)

if __name__ == "__main__":
    # Test the resume parser
    parser = ResumeParser()
    
    # Test with a sample PDF (you'll need to provide a real path)
    test_pdf = "./data/resume.pdf"
    
    if Path(test_pdf).exists():
        result = parser.parse_full_resume(test_pdf)
        print("Parsed Resume Data:")
        print(f"Contact Info: {result.get('contact_info', {})}")
        print(f"Skills: {result.get('skills', [])}")
        print(f"Experience Years: {result.get('experience_years', 'Unknown')}")
        print(f"Education: {result.get('education', [])}")
        print(f"\nSummary: {parser.get_summary_for_applications()}")
    else:
        print(f"Test PDF not found at {test_pdf}")
        print("Please place a resume PDF at ./data/resume.pdf to test")
