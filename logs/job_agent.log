2025-06-02 20:09:01 | INFO | utils.security:_generate_key:45 | Generated new encryption key
2025-06-02 20:09:01 | WARNING | utils.security:decrypt_data:67 | No encrypted data file found
2025-06-02 20:09:06 | INFO | automation.job_searcher:initialize_browser:33 | <PERSON>rowser initialized successfully
2025-06-02 20:09:06 | INFO | automation.job_searcher:search_linkedin_jobs:78 | Searching LinkedIn: https://www.linkedin.com/jobs/search?keywords=software engineer&location=remote&f_E=2&f_TPR=r86400&sortBy=DD
2025-06-02 20:09:06 | ERROR | automation.job_searcher:_extract_jobs_with_ai:210 | Error extracting jobs with AI: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download
2025-06-02 20:09:06 | INFO | automation.job_searcher:search_linkedin_jobs:93 | Found 0 jobs on LinkedIn
2025-06-02 20:15:00 | WARNING | utils.security:decrypt_data:67 | No encrypted data file found
2025-06-02 20:15:05 | WARNING | utils.security:decrypt_data:67 | No encrypted data file found
2025-06-02 20:19:06 | INFO | utils.security:decrypt_data:76 | Data decrypted successfully
2025-06-02 20:19:06 | INFO | core.user_data:load_user_data:93 | User data loaded successfully
2025-06-02 20:19:14 | ERROR | integrations.notion_client:test_connection:37 | Notion API error: API token is invalid.
2025-06-02 20:19:15 | ERROR | integrations.email_client:test_connection:52 | Email connection test failed: (535, b'5.7.8 Username and Password not accepted. For more information, go to\n5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506bd9448sm77547355ad.66 - gsmtp')
2025-06-02 20:19:20 | INFO | scheduler.daily_runner:run_manual:251 | Running manual job application process...
2025-06-02 20:19:20 | INFO | scheduler.daily_runner:run_once:224 | Running job process once...
2025-06-02 20:19:20 | INFO | scheduler.daily_runner:run_daily_job_process:37 | Starting daily job application process
2025-06-02 20:19:20 | INFO | utils.security:decrypt_data:76 | Data decrypted successfully
2025-06-02 20:19:20 | INFO | core.user_data:load_user_data:93 | User data loaded successfully
2025-06-02 20:19:20 | INFO | scheduler.daily_runner:run_daily_job_process:56 | Step 1: Searching for new jobs...
2025-06-02 20:19:21 | INFO | automation.job_searcher:initialize_browser:34 | Browser initialized successfully
2025-06-02 20:19:22 | INFO | automation.job_searcher:search_linkedin_jobs:81 | Searching LinkedIn: https://www.linkedin.com/jobs/search?keywords=Software Engineer OR Cloud Engineer OR DevOps Engineer OR Backend Developer&location=Remote OR San Francisco OR New York OR Seattle&f_E=2,1&f_TPR=r86400&sortBy=DD
2025-06-02 20:19:50 | INFO | automation.job_searcher:search_linkedin_jobs:96 | Found 0 jobs on LinkedIn
2025-06-02 20:19:51 | INFO | automation.job_searcher:search_indeed_jobs:122 | Searching Indeed: https://www.indeed.com/jobs?q=Software Engineer Cloud Engineer DevOps Engineer Backend Developer&l=Remote&fromage=1&sort=date
2025-06-02 20:20:21 | ERROR | automation.job_searcher:search_indeed_jobs:134 | Error searching Indeed jobs: Timeout 30000ms exceeded.
2025-06-02 20:20:22 | INFO | automation.job_searcher:search_glassdoor_jobs:156 | Searching Glassdoor: https://www.glassdoor.com/Job/jobs.htm?sc.keyword=Software Engineer Cloud Engineer DevOps Engineer Backend Developer&locT=&locId=&jobType=
2025-06-02 20:20:46 | ERROR | automation.job_searcher:_parse_ai_job_response:249 | Error parsing AI job response: Expecting value: line 32 column 3 (char 1331)
2025-06-02 20:20:46 | INFO | automation.job_searcher:search_glassdoor_jobs:165 | Found 0 jobs on Glassdoor
2025-06-02 20:20:46 | INFO | automation.job_searcher:search_all_platforms:342 | Found 0 unique jobs across all platforms
2025-06-02 20:20:46 | INFO | automation.job_searcher:close_browser:47 | Browser closed
2025-06-02 20:20:46 | INFO | scheduler.daily_runner:run_daily_job_process:61 | No new jobs found today
2025-06-02 20:20:54 | ERROR | integrations.notion_client:get_applications_by_status:228 | Failed to retrieve applications by status: API token is invalid.
2025-06-02 20:21:01 | ERROR | integrations.notion_client:get_applications_by_status:228 | Failed to retrieve applications by status: API token is invalid.
2025-06-02 20:21:09 | ERROR | integrations.notion_client:get_daily_application_count:253 | Failed to get daily application count: API token is invalid.
2025-06-02 20:21:09 | ERROR | integrations.email_client:_send_email:296 | Failed to send email: (535, b'5.7.8 Username and Password not accepted. For more information, go to\n5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e3c1358sm6975247a91.37 - gsmtp')
2025-06-02 20:21:09 | ERROR | scheduler.daily_runner:_send_daily_summary:194 | Failed to send daily summary: object bool can't be used in 'await' expression
