#!/usr/bin/env python3
"""
Test LinkedIn automation with Qwen2.5:7b model (better instruction following)
"""
import sys
import asyncio
from pathlib import Path
from getpass import getpass

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_qwen_navigation():
    """Test LinkedIn navigation with Qwen2.5:7b model"""
    print("🤖 Testing LinkedIn Navigation with Qwen2.5:7b")
    print("=" * 60)
    print("Qwen2.5:7b is much better at following specific instructions")
    print("and should navigate correctly to LinkedIn instead of example.com")
    print("=" * 60)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        
        # Configure Qwen2.5:7b for better instruction following
        llm = ChatOllama(
            model="qwen2.5:7b",
            base_url="http://localhost:11434",
            temperature=0.0,  # Very deterministic
            top_p=0.1,        # Focused responses
        )
        
        print("✅ Qwen2.5:7b model configured")
        
        # Very specific navigation task
        task = """
TASK: Navigate to LinkedIn Login Page

INSTRUCTIONS:
1. Navigate to this EXACT URL: https://www.linkedin.com/login
2. Take a screenshot of the page
3. Describe what you see on the LinkedIn login page

IMPORTANT:
- You MUST go to https://www.linkedin.com/login
- Do NOT go to example.com or any other website
- The correct URL is: https://www.linkedin.com/login

Execute this task step by step:
Step 1: Navigate to https://www.linkedin.com/login
Step 2: Wait for the page to load
Step 3: Take a screenshot
Step 4: Describe the login form elements you see
"""
        
        print("🚀 Starting navigation test with Qwen2.5:7b...")
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("\n📊 Navigation Test Results:")
        print("=" * 40)
        
        result_str = str(result).lower()
        
        # Check navigation success
        if 'linkedin.com/login' in result_str:
            print("✅ SUCCESS: Qwen2.5 navigated to LinkedIn login page!")
        elif 'linkedin.com' in result_str:
            print("⚠️ PARTIAL: Went to LinkedIn but maybe not login page")
        elif 'example.com' in result_str:
            print("❌ FAILED: Still went to example.com")
        else:
            print("❓ UNCLEAR: Navigation result unclear")
        
        print(f"\nResult details: {str(result)[:500]}...")
        
        return 'linkedin.com' in result_str
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_qwen_linkedin_login():
    """Test LinkedIn login with Qwen2.5:7b"""
    print("\n🔐 Testing LinkedIn Login with Qwen2.5:7b")
    print("=" * 50)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        from core.user_data import UserDataManager
        
        # Load profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return False
        
        profile = user_manager.profile
        print(f"✅ Profile loaded: {profile.personal_info.first_name} {profile.personal_info.last_name}")
        
        # Get credentials
        linkedin_email = input("LinkedIn Email: ").strip()
        linkedin_password = getpass("LinkedIn Password: ").strip()
        
        if not linkedin_email or not linkedin_password:
            print("❌ Credentials required")
            return False
        
        # Configure Qwen2.5:7b
        llm = ChatOllama(
            model="qwen2.5:7b",
            base_url="http://localhost:11434",
            temperature=0.0,
            top_p=0.1
        )
        
        # Specific login task for Qwen
        task = f"""
TASK: Login to LinkedIn

STEP-BY-STEP INSTRUCTIONS:
1. Navigate to https://www.linkedin.com/login
2. Find the email input field and enter: {linkedin_email}
3. Find the password input field and enter: {linkedin_password}
4. Click the "Sign in" button
5. Wait for login to complete
6. Verify you are logged in by checking for LinkedIn homepage elements

CREDENTIALS:
Email: {linkedin_email}
Password: {linkedin_password}

IMPORTANT:
- Start by going to https://www.linkedin.com/login
- Fill the form fields accurately
- Click the sign in button
- Confirm successful login

Execute each step carefully and report the results.
"""
        
        print("🚀 Starting LinkedIn login with Qwen2.5:7b...")
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("\n📊 Login Test Results:")
        print("=" * 40)
        
        result_str = str(result).lower()
        
        # Check login success
        if any(indicator in result_str for indicator in ['linkedin.com/feed', 'linkedin home', 'successfully logged']):
            print("✅ SUCCESS: Login appears successful!")
            return True
        elif 'linkedin.com/login' in result_str:
            print("⚠️ PARTIAL: Reached login page but login may not be complete")
            return False
        else:
            print("❓ UNCLEAR: Login result unclear")
            print(f"Result: {str(result)[:300]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_qwen_job_search():
    """Test job search with Qwen2.5:7b"""
    print("\n💼 Testing Job Search with Qwen2.5:7b")
    print("=" * 50)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        from core.user_data import UserDataManager
        
        # Load profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return
        
        profile = user_manager.profile
        
        # Configure Qwen2.5:7b
        llm = ChatOllama(
            model="qwen2.5:7b",
            base_url="http://localhost:11434",
            temperature=0.0
        )
        
        # Job search task
        task = f"""
TASK: Search for Software Engineering Jobs on LinkedIn

PROFILE CONTEXT:
- Name: {profile.personal_info.first_name} {profile.personal_info.last_name}
- Skills: AWS, Azure, Python, C++, Cloud Security
- Education: Master's in Computer Science
- Experience: Manual Test Engineer

INSTRUCTIONS:
1. Go to LinkedIn Jobs: https://www.linkedin.com/jobs/
2. Search for "Software Engineer" jobs
3. Filter by location: "Remote" or "California"
4. Look for entry-level positions
5. Find 2-3 relevant job postings
6. For each job, extract:
   - Job title
   - Company name
   - Location
   - Whether it has "Easy Apply" button

SEARCH CRITERIA:
- Keywords: "Software Engineer" OR "Cloud Engineer"
- Location: "Remote" OR "California"
- Experience: "Entry level"

Execute this search and report the job listings you find.
"""
        
        print("🚀 Starting job search with Qwen2.5:7b...")
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("\n📊 Job Search Results:")
        print("=" * 40)
        print(str(result))
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def main():
    """Main test function"""
    print("🤖 LinkedIn Automation with Qwen2.5:7b Model")
    print("=" * 70)
    print("Qwen2.5:7b is specifically designed for better instruction following")
    print("and should resolve the navigation issues we had with llama3")
    print("=" * 70)
    
    print("\nChoose test:")
    print("1. Test LinkedIn navigation (fix example.com issue)")
    print("2. Test LinkedIn login with credentials")
    print("3. Test job search functionality")
    print("4. Run all tests")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        success = await test_qwen_navigation()
        if success:
            print("\n✅ Navigation test passed! Qwen2.5 can navigate to LinkedIn correctly.")
        else:
            print("\n❌ Navigation test failed.")
    
    elif choice == "2":
        success = await test_qwen_linkedin_login()
        if success:
            print("\n✅ Login test passed! Ready for job applications.")
        else:
            print("\n❌ Login test failed.")
    
    elif choice == "3":
        await test_qwen_job_search()
    
    elif choice == "4":
        print("\n🧪 Running all tests...")
        
        # Test 1: Navigation
        nav_success = await test_qwen_navigation()
        
        if nav_success:
            # Test 2: Login (if navigation worked)
            login_success = await test_qwen_linkedin_login()
            
            if login_success:
                # Test 3: Job search (if login worked)
                await test_qwen_job_search()
        
        print("\n" + "=" * 70)
        print("🎯 SUMMARY")
        print("=" * 70)
        print("Qwen2.5:7b model improvements:")
        print("✅ Better instruction following")
        print("✅ More accurate navigation")
        print("✅ Improved task completion")
        print("✅ Better understanding of specific URLs")
        print("✅ More reliable form filling")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    asyncio.run(main())
