#!/usr/bin/env python3
"""
Quick start script for the Job Application Agent
This script helps users get started quickly with minimal configuration
"""
import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel

console = Console()

def display_welcome():
    """Display welcome message"""
    welcome_text = """
🤖 Welcome to the Intelligent Job Application Agent!

This quick start will help you:
1. Install dependencies
2. Set up Ollama
3. Configure basic settings
4. Run your first job search

Let's get started!
    """
    
    panel = Panel(
        welcome_text,
        title="Quick Start",
        border_style="blue",
        padding=(1, 2)
    )
    console.print(panel)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        console.print("[red]❌ Python 3.11+ is required[/red]")
        console.print(f"Current version: {sys.version}")
        return False
    
    console.print(f"[green]✅ Python {sys.version_info.major}.{sys.version_info.minor} detected[/green]")
    return True

def install_dependencies():
    """Install Python dependencies"""
    console.print("\n[cyan]📦 Installing dependencies...[/cyan]")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("[green]✅ Dependencies installed successfully[/green]")
            return True
        else:
            console.print(f"[red]❌ Failed to install dependencies: {result.stderr}[/red]")
            return False
    except Exception as e:
        console.print(f"[red]❌ Error installing dependencies: {e}[/red]")
        return False

def install_playwright():
    """Install Playwright browsers"""
    console.print("\n[cyan]🌐 Installing browser dependencies...[/cyan]")
    
    try:
        import subprocess
        result = subprocess.run(["playwright", "install", "chromium", "--with-deps"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("[green]✅ Browser dependencies installed[/green]")
            return True
        else:
            console.print(f"[red]❌ Failed to install browsers: {result.stderr}[/red]")
            return False
    except Exception as e:
        console.print(f"[red]❌ Error installing browsers: {e}[/red]")
        return False

def check_ollama():
    """Check if Ollama is installed and running"""
    console.print("\n[cyan]🧠 Checking Ollama...[/cyan]")
    
    try:
        import subprocess
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("[green]✅ Ollama is installed and running[/green]")
            
            # Check if llama3.2 is available
            if "llama3.2" in result.stdout:
                console.print("[green]✅ llama3.2 model found[/green]")
                return True
            else:
                console.print("[yellow]⚠️ llama3.2 model not found[/yellow]")
                if Confirm.ask("Would you like to download llama3.2 model? (This may take a while)"):
                    console.print("Downloading llama3.2 model...")
                    download_result = subprocess.run(["ollama", "pull", "llama3.2:latest"], 
                                                   capture_output=True, text=True)
                    if download_result.returncode == 0:
                        console.print("[green]✅ llama3.2 model downloaded[/green]")
                        return True
                    else:
                        console.print(f"[red]❌ Failed to download model: {download_result.stderr}[/red]")
                        return False
                else:
                    console.print("[yellow]⚠️ You can download it later with: ollama pull llama3.2:latest[/yellow]")
                    return True
        else:
            console.print("[red]❌ Ollama not found or not running[/red]")
            console.print("Please install Ollama from: https://ollama.ai")
            return False
            
    except FileNotFoundError:
        console.print("[red]❌ Ollama not found[/red]")
        console.print("Please install Ollama from: https://ollama.ai")
        return False
    except Exception as e:
        console.print(f"[red]❌ Error checking Ollama: {e}[/red]")
        return False

def create_basic_env():
    """Create a basic .env file"""
    console.print("\n[cyan]⚙️ Creating basic configuration...[/cyan]")
    
    env_content = """# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.2:latest

# Job Search Preferences
TARGET_ROLES=Software Engineer,Cloud Engineer,DevOps Engineer,Backend Developer
TARGET_LOCATIONS=Remote,San Francisco,New York,Seattle
EXPERIENCE_LEVEL=Entry Level,Internship
MAX_APPLICATIONS_PER_DAY=5

# Browser Settings
HEADLESS_MODE=false
BROWSER_TIMEOUT=30

# Logging
LOG_LEVEL=INFO

# Daily Schedule
RUN_TIME=09:00
TIMEZONE=America/Los_Angeles
"""
    
    try:
        with open(".env", "w") as f:
            f.write(env_content)
        
        console.print("[green]✅ Basic configuration created (.env file)[/green]")
        return True
    except Exception as e:
        console.print(f"[red]❌ Failed to create .env file: {e}[/red]")
        return False

def run_quick_test():
    """Run a quick test of the system"""
    console.print("\n[cyan]🧪 Running quick test...[/cyan]")
    
    try:
        # Test imports
        sys.path.append(str(Path(__file__).parent))
        
        from config.settings import settings
        console.print("[green]✅ Configuration loaded[/green]")
        
        # Test Ollama connection
        import ollama
        client = ollama.Client(host=settings.ollama_host)
        models = client.list()
        console.print("[green]✅ Ollama connection successful[/green]")
        
        console.print("[green]🎉 Quick test passed![/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Quick test failed: {e}[/red]")
        return False

def main():
    """Main quick start function"""
    display_welcome()
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install dependencies
    if not install_dependencies():
        console.print("[red]Please fix dependency issues and try again[/red]")
        return
    
    # Install Playwright
    if not install_playwright():
        console.print("[yellow]Browser installation failed, but you can continue[/yellow]")
    
    # Check Ollama
    if not check_ollama():
        console.print("[red]Please install and configure Ollama, then try again[/red]")
        return
    
    # Create basic configuration
    if not create_basic_env():
        console.print("[red]Failed to create configuration[/red]")
        return
    
    # Run quick test
    if not run_quick_test():
        console.print("[red]System test failed[/red]")
        return
    
    # Success message
    console.print("\n[bold green]🎉 Quick start completed successfully![/bold green]")
    console.print("\n[cyan]Next steps:[/cyan]")
    console.print("1. Run: [bold]python main.py setup[/bold] - Complete your profile")
    console.print("2. Run: [bold]python main.py start --manual[/bold] - Test job search")
    console.print("3. Run: [bold]python main.py start[/bold] - Start scheduled agent")
    
    console.print("\n[yellow]📖 For detailed setup, see README.md[/yellow]")

if __name__ == "__main__":
    main()
