#!/usr/bin/env python3
"""
Intelligent Job Application Agent
Main entry point for the autonomous job application system
"""
import asyncio
import sys
from pathlib import Path
from typing import Optional
import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from loguru import logger

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config.settings import settings, setup_directories
from core.user_data import UserDataManager
from core.resume_parser import ResumeParser
from scheduler.daily_runner import JobApplicationOrchestrator
from integrations.notion_client import NotionJobTracker
from integrations.email_client import EmailNotificationClient
from utils.security import generate_encryption_key

console = Console()

def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add file handler
    logger.add(
        settings.log_file_path,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="30 days"
    )
    
    # Add console handler for INFO and above
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}"
    )

def display_banner():
    """Display application banner"""
    banner_text = Text()
    banner_text.append("🤖 Intelligent Job Application Agent\n", style="bold blue")
    banner_text.append("Powered by browser-use + Ollama\n", style="cyan")
    banner_text.append("Autonomous job searching and application system", style="white")
    
    panel = Panel(
        banner_text,
        title="Job Application Agent",
        border_style="blue",
        padding=(1, 2)
    )
    console.print(panel)

@click.group()
def cli():
    """Intelligent Job Application Agent CLI"""
    setup_directories()
    setup_logging()
    display_banner()

@cli.command()
def setup():
    """Initial setup - collect user data and configure the system"""
    console.print("\n[bold blue]🚀 Initial Setup[/bold blue]")
    
    try:
        # User data setup
        user_manager = UserDataManager()
        
        if user_manager.load_user_data():
            console.print("[yellow]Existing profile found.[/yellow]")
            if not click.confirm("Do you want to reconfigure your profile?"):
                user_manager.display_profile_summary()
                return
        
        # Collect user data
        console.print("\n[cyan]Collecting user information...[/cyan]")
        if user_manager.collect_initial_data():
            console.print("\n[green]✓ User profile created successfully![/green]")
        else:
            console.print("\n[red]✗ Profile setup failed[/red]")
            return
        
        # Resume setup
        console.print("\n[cyan]Resume Setup[/cyan]")
        resume_path = click.prompt("Path to your resume PDF", default="./data/resume.pdf")
        
        if Path(resume_path).exists():
            parser = ResumeParser()
            parsed_data = parser.parse_full_resume(resume_path)
            
            if parsed_data:
                console.print(f"[green]✓ Resume parsed successfully[/green]")
                console.print(f"Found {len(parsed_data.get('skills', []))} skills")
                
                # Update user profile with resume path
                user_manager.profile.resume_path = resume_path
                user_manager.save_user_data()
            else:
                console.print("[yellow]⚠ Resume parsing failed, but path saved[/yellow]")
        else:
            console.print(f"[yellow]⚠ Resume file not found at {resume_path}[/yellow]")
            console.print("Please place your resume PDF at the specified path")
        
        # Notion setup
        console.print("\n[cyan]Notion Integration Setup[/cyan]")
        if click.confirm("Do you want to set up Notion integration for job tracking?"):
            notion_key = click.prompt("Notion API Key", hide_input=True)
            database_id = click.prompt("Notion Database ID")
            
            # Test Notion connection
            tracker = NotionJobTracker(notion_key, database_id)
            if tracker.test_connection():
                console.print("[green]✓ Notion connection successful[/green]")
                
                # Update .env file or save to config
                env_path = Path(".env")
                if env_path.exists():
                    with open(env_path, "a") as f:
                        f.write(f"\nNOTION_API_KEY={notion_key}\n")
                        f.write(f"NOTION_DATABASE_ID={database_id}\n")
                else:
                    with open(env_path, "w") as f:
                        f.write(f"NOTION_API_KEY={notion_key}\n")
                        f.write(f"NOTION_DATABASE_ID={database_id}\n")
                
                console.print("[green]✓ Notion credentials saved[/green]")
            else:
                console.print("[red]✗ Notion connection failed[/red]")
        
        # Email setup
        console.print("\n[cyan]Email Notifications Setup[/cyan]")
        if click.confirm("Do you want to set up email notifications?"):
            email_addr = click.prompt("Email address")
            email_pass = click.prompt("Email app password", hide_input=True)
            
            # Test email connection
            email_client = EmailNotificationClient(email_addr, email_pass)
            if email_client.test_connection():
                console.print("[green]✓ Email connection successful[/green]")
                
                # Save to .env
                with open(".env", "a") as f:
                    f.write(f"\nEMAIL_ADDRESS={email_addr}\n")
                    f.write(f"EMAIL_PASSWORD={email_pass}\n")
                
                console.print("[green]✓ Email credentials saved[/green]")
            else:
                console.print("[red]✗ Email connection failed[/red]")
        
        console.print("\n[bold green]🎉 Setup completed successfully![/bold green]")
        console.print("You can now run 'python main.py start' to begin the job application process.")
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Setup cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Setup error: {e}[/red]")
        logger.error(f"Setup error: {e}")

@cli.command()
@click.option('--manual', is_flag=True, help='Run once manually instead of scheduling')
def start(manual: bool):
    """Start the job application agent"""
    console.print("\n[bold blue]🚀 Starting Job Application Agent[/bold blue]")
    
    try:
        orchestrator = JobApplicationOrchestrator()
        
        if manual:
            console.print("[cyan]Running manual job application process...[/cyan]")
            asyncio.run(orchestrator.run_manual())
        else:
            console.print("[cyan]Starting scheduled job application agent...[/cyan]")
            console.print(f"[yellow]Scheduled to run daily at {settings.run_time}[/yellow]")
            console.print("[yellow]Press Ctrl+C to stop[/yellow]")
            asyncio.run(orchestrator.setup_and_run())
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Job application agent stopped by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")
        logger.error(f"Runtime error: {e}")

@cli.command()
def status():
    """Show system status and configuration"""
    console.print("\n[bold blue]📊 System Status[/bold blue]")
    
    # User data status
    user_manager = UserDataManager()
    if user_manager.load_user_data():
        console.print("[green]✓ User profile loaded[/green]")
        user_manager.display_profile_summary()
        
        missing_fields = user_manager.check_missing_fields()
        if missing_fields:
            console.print(f"\n[yellow]⚠ Missing configuration: {', '.join(missing_fields)}[/yellow]")
    else:
        console.print("[red]✗ No user profile found[/red]")
        console.print("Run 'python main.py setup' to create your profile")
        return
    
    # Notion status
    console.print("\n[cyan]Notion Integration:[/cyan]")
    tracker = NotionJobTracker()
    if tracker.test_connection():
        console.print("[green]✓ Connected[/green]")
    else:
        console.print("[red]✗ Not connected[/red]")
    
    # Email status
    console.print("\n[cyan]Email Notifications:[/cyan]")
    email_client = EmailNotificationClient()
    if email_client.test_connection():
        console.print("[green]✓ Connected[/green]")
    else:
        console.print("[red]✗ Not connected[/red]")
    
    # Ollama status
    console.print("\n[cyan]Ollama LLM:[/cyan]")
    try:
        import ollama
        client = ollama.Client(host=settings.ollama_host)
        models = client.list()
        if any(model['name'] == settings.ollama_model for model in models['models']):
            console.print(f"[green]✓ Model {settings.ollama_model} available[/green]")
        else:
            console.print(f"[yellow]⚠ Model {settings.ollama_model} not found[/yellow]")
    except Exception as e:
        console.print(f"[red]✗ Connection failed: {e}[/red]")
    
    # Configuration summary
    console.print(f"\n[cyan]Configuration:[/cyan]")
    console.print(f"  Max applications per day: {settings.max_applications_per_day}")
    console.print(f"  Target roles: {', '.join(settings.target_roles)}")
    console.print(f"  Target locations: {', '.join(settings.target_locations)}")
    console.print(f"  Scheduled run time: {settings.run_time}")

@cli.command()
def test():
    """Test system components"""
    console.print("\n[bold blue]🧪 Testing System Components[/bold blue]")
    
    async def run_tests():
        # Test job search
        console.print("\n[cyan]Testing job search...[/cyan]")
        try:
            from automation.job_searcher import JobSearcher
            searcher = JobSearcher()
            
            # Test with a simple search
            jobs = await searcher.search_linkedin_jobs(
                keywords=["software engineer"],
                locations=["remote"],
                experience_levels=["entry level"],
                max_results=5
            )
            
            console.print(f"[green]✓ Found {len(jobs)} test jobs[/green]")
            
        except Exception as e:
            console.print(f"[red]✗ Job search test failed: {e}[/red]")
        
        # Test resume parsing
        console.print("\n[cyan]Testing resume parsing...[/cyan]")
        try:
            parser = ResumeParser()
            if Path(settings.resume_path).exists():
                result = parser.parse_full_resume(settings.resume_path)
                console.print(f"[green]✓ Resume parsed, found {len(result.get('skills', []))} skills[/green]")
            else:
                console.print(f"[yellow]⚠ Resume not found at {settings.resume_path}[/yellow]")
        except Exception as e:
            console.print(f"[red]✗ Resume parsing test failed: {e}[/red]")
    
    try:
        asyncio.run(run_tests())
    except Exception as e:
        console.print(f"[red]Test error: {e}[/red]")

@cli.command()
def generate_key():
    """Generate a new encryption key for secure data storage"""
    key = generate_encryption_key()
    console.print(f"\n[cyan]Generated encryption key:[/cyan]")
    console.print(f"[yellow]{key}[/yellow]")
    console.print("\n[cyan]Add this to your .env file as:[/cyan]")
    console.print(f"ENCRYPTION_KEY={key}")

if __name__ == "__main__":
    cli()
