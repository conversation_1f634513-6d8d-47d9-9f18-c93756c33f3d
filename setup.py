#!/usr/bin/env python3
"""
Setup script for the Job Application Agent
"""
from setuptools import setup, find_packages
from pathlib import Path

# Read README for long description
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="job-application-agent",
    version="1.0.0",
    description="Intelligent autonomous job application agent using browser-use and Ollama",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Job Application Agent Team",
    author_email="<EMAIL>",
    url="https://github.com/your-username/job-application-agent",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.11",
    entry_points={
        "console_scripts": [
            "job-agent=main:cli",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="job application automation browser-use ollama ai agent",
    project_urls={
        "Bug Reports": "https://github.com/your-username/job-application-agent/issues",
        "Source": "https://github.com/your-username/job-application-agent",
        "Documentation": "https://github.com/your-username/job-application-agent/blob/main/README.md",
    },
)
