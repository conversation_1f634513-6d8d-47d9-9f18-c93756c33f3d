#!/usr/bin/env python3
"""
LinkedIn Job Application Bot - USING BROWSER-USE
Complete autonomous job application system with browser-use + Ollama
"""
import sys
import asyncio
import json
from pathlib import Path
from getpass import getpass
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def main():
    """Main LinkedIn job application bot using browser-use"""
    print("🤖 LinkedIn Job Application Bot - BROWSER-USE VERSION")
    print("=" * 80)
    print("✅ Uses browser-use library for intelligent automation")
    print("✅ Qwen2.5:7b model for better instruction following")
    print("✅ Complete job search and application workflow")
    print("✅ Automated form filling and question answering")
    print("✅ Local AI processing with Ollama")
    print("=" * 80)
    
    try:
        # Check browser-use availability
        try:
            from browser_use import Agent
            from langchain_ollama import ChatOllama
            print("✅ browser-use library available")
        except ImportError:
            print("❌ browser-use not available. Please install:")
            print("   pip install browser-use langchain-ollama")
            return
        
        # Import modules
        from automation.working_browser_use_bot import WorkingBrowserUseBot
        from core.user_data import UserDataManager
        
        # Load user profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found. Please run setup first.")
            return
        
        profile = user_manager.profile
        personal_info = profile.personal_info
        
        print(f"✅ Profile loaded: {personal_info.first_name} {personal_info.last_name}")
        print(f"📧 Email: {personal_info.email}")
        print(f"📱 Phone: {personal_info.phone}")
        print(f"🎓 Education: {profile.education[0].degree} in {profile.education[0].field_of_study}")
        print(f"💼 Experience: {profile.work_experience[0].position} at {profile.work_experience[0].company}")
        print(f"🛠️ Skills: {', '.join(profile.skills[:8])}...")
        
        # Convert profile to dict for the bot
        user_profile_dict = {
            "personal_info": {
                "first_name": personal_info.first_name,
                "last_name": personal_info.last_name,
                "email": personal_info.email,
                "phone": personal_info.phone,
                "address": personal_info.address,
                "city": personal_info.city,
                "state": personal_info.state,
                "zip_code": personal_info.zip_code
            },
            "work_experience": [
                {
                    "position": exp.position,
                    "company": exp.company,
                    "start_date": exp.start_date,
                    "end_date": exp.end_date,
                    "description": exp.description
                } for exp in profile.work_experience
            ],
            "education": [
                {
                    "degree": edu.degree,
                    "field_of_study": edu.field_of_study,
                    "institution": edu.institution,
                    "graduation_date": edu.graduation_date,
                    "gpa": edu.gpa
                } for edu in profile.education
            ],
            "skills": profile.skills,
            "certifications": profile.certifications,
            "application_answers": {
                "why_interested": profile.application_answers.why_interested,
                "greatest_strength": profile.application_answers.greatest_strength,
                "career_goals": profile.application_answers.career_goals,
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "salary_expectation": profile.application_answers.salary_expectation,
                "willing_to_relocate": profile.application_answers.willing_to_relocate,
                "preferred_work_arrangement": profile.application_answers.preferred_work_arrangement
            },
            "resume_path": profile.resume_path
        }
        
        # Get LinkedIn credentials
        print("\n🔐 LinkedIn Credentials Required")
        linkedin_email = input("LinkedIn Email: ").strip()
        if not linkedin_email:
            print("❌ LinkedIn email is required")
            return
        
        linkedin_password = getpass("LinkedIn Password: ").strip()
        if not linkedin_password:
            print("❌ LinkedIn password is required")
            return
        
        # Get automation settings
        print("\n⚙️ Automation Settings")
        mode = input("Mode (1=Complete automation, 2=Apply to specific job): ").strip()
        
        if mode == "2":
            # Apply to specific job
            job_url = input("Enter LinkedIn job URL: ").strip()
            if not job_url:
                print("❌ Job URL is required")
                return
            
            print(f"\n🎯 Applying to specific job: {job_url}")
            print("🚀 Starting browser-use automation...")
            
            # Initialize bot
            bot = WorkingBrowserUseBot(user_profile_dict)
            
            # Login first
            login_success = await bot.login_to_linkedin(linkedin_email, linkedin_password)
            if not login_success:
                print("❌ Failed to login to LinkedIn")
                return
            
            # Apply to specific job
            result = await bot.apply_to_specific_job(job_url)
            
            # Display results
            print("\n📊 Application Results:")
            print("=" * 40)
            if result.get("success", False):
                print("✅ Application submitted successfully!")
                print(f"   Job URL: {result['job_url']}")
                print(f"   Applied at: {result['applied_at']}")
            else:
                print("❌ Application failed")
                if "error" in result:
                    print(f"   Error: {result['error']}")
            
        else:
            # Complete automation
            max_applications = input("Maximum applications today (default: 5): ").strip()
            max_applications = int(max_applications) if max_applications.isdigit() else 5
            
            # Confirm settings
            print(f"\n📋 Automation Summary:")
            print(f"   👤 Profile: {personal_info.first_name} {personal_info.last_name}")
            print(f"   📧 LinkedIn: {linkedin_email}")
            print(f"   🎯 Max Applications: {max_applications}")
            print(f"   🔍 Target Roles: Software Engineer, Cloud Engineer, DevOps Engineer")
            print(f"   📍 Locations: Remote, California, San Francisco")
            print(f"   🤖 AI Model: Qwen2.5:7b via Ollama")
            print(f"   🌐 Automation: browser-use + Playwright")
            
            confirm = input("\n🚀 Start automation? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ Automation cancelled")
                return
            
            # Run complete automation
            print("\n🤖 Starting Complete LinkedIn Automation with browser-use...")
            print("=" * 60)
            
            bot = WorkingBrowserUseBot(user_profile_dict)
            results = await bot.run_complete_automation(linkedin_email, linkedin_password, max_applications)
            
            # Display results
            print("\n" + "=" * 60)
            print("🎉 AUTOMATION COMPLETED!")
            print("=" * 60)
            
            if "error" in results:
                print(f"❌ Error: {results['error']}")
            else:
                print(f"📊 Results Summary:")
                print(f"   📝 Total Applications: {results.get('total_applications', 0)}")
                print(f"   ✅ Successful: {results.get('successful_applications', 0)}")
                
                applications = results.get('applications', [])
                if applications:
                    print(f"\n✅ Applications Submitted:")
                    for app in applications:
                        print(f"   • {app.get('title', 'Unknown')} at {app.get('company', 'Unknown')}")
                        print(f"     Method: {app.get('method', 'Unknown')}")
                        print(f"     Status: {'✅ Success' if app.get('success', False) else '❌ Failed'}")
                        print(f"     Applied: {app.get('applied_at', 'Unknown')}")
                        print()
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"browser_use_results_{timestamp}.json"
        
        if mode == "2":
            save_data = result
        else:
            save_data = results
        
        with open(results_file, 'w') as f:
            json.dump(save_data, f, indent=2)
        
        print(f"📄 Results saved to: {results_file}")
        
        print("\n🎯 What was accomplished with browser-use:")
        print("✅ Intelligent web automation with AI")
        print("✅ LinkedIn login and navigation")
        print("✅ Job search with filters")
        print("✅ Easy Apply automation")
        print("✅ Form filling with profile data")
        print("✅ Question answering with AI")
        print("✅ Application submission")
        print("✅ Result tracking and reporting")
        
        print("\n🔒 Privacy & Security:")
        print("✅ All AI processing via local Ollama")
        print("✅ No data sent to external AI services")
        print("✅ Credentials used only for automation session")
        print("✅ browser-use provides intelligent automation")
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Please install required packages:")
        print("pip install browser-use langchain-ollama")
    except KeyboardInterrupt:
        print("\n⚠️ Automation interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_browser_use():
    """Test browser-use functionality"""
    print("🧪 Testing browser-use Integration")
    print("=" * 40)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        
        # Test LLM
        llm = ChatOllama(
            model="qwen2.5:7b",
            base_url="http://localhost:11434",
            temperature=0.0
        )
        
        print("✅ browser-use imported successfully")
        print("✅ Qwen2.5:7b model configured")
        
        # Simple test task
        task = "Go to https://www.linkedin.com and take a screenshot"
        
        agent = Agent(task=task, llm=llm)
        print("✅ Agent created successfully")
        
        print("🚀 Running test automation...")
        result = await agent.run()
        
        print("✅ Test completed successfully!")
        print(f"Result: {str(result)[:200]}...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        asyncio.run(test_browser_use())
    else:
        asyncio.run(main())
