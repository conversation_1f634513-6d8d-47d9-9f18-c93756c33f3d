version: '3.8'

services:
  job-agent:
    build: .
    container_name: job-application-agent
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - HEADLESS_MODE=true
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      - ollama
    networks:
      - job-agent-network

  ollama:
    image: ollama/ollama:latest
    container_name: ollama-server
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_KEEP_ALIVE=24h
    networks:
      - job-agent-network
    # Uncomment if you have GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  ollama_data:
    driver: local

networks:
  job-agent-network:
    driver: bridge
