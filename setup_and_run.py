#!/usr/bin/env python3
"""
Setup and Run LinkedIn Job Application Bot
Complete setup and execution script
"""
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        "selenium",
        "loguru", 
        "webdriver-manager",
        "pydantic",
        "cryptography",
        "PyPDF2",
        "pdfplumber",
        "requests",
        "beautifulsoup4",
        "click",
        "rich"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """Install missing dependencies"""
    if not packages:
        return True
    
    print(f"\n📦 Installing {len(packages)} missing packages...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install"
        ] + packages)
        print("✅ All dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_profile():
    """Check if user profile exists"""
    try:
        from core.user_data import UserDataManager
        
        user_manager = UserDataManager()
        if user_manager.load_user_data():
            profile = user_manager.profile
            print(f"✅ Profile found: {profile.personal_info.first_name} {profile.personal_info.last_name}")
            return True
        else:
            print("❌ No user profile found")
            return False
    except Exception as e:
        print(f"❌ Error checking profile: {e}")
        return False

def create_profile():
    """Create user profile from resume"""
    print("\n📋 Creating user profile...")
    
    try:
        # Run the profile creation script
        subprocess.check_call([sys.executable, "create_profile.py"])
        print("✅ Profile created successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create profile: {e}")
        return False

def run_linkedin_bot():
    """Run the LinkedIn job application bot"""
    print("\n🤖 Starting LinkedIn Job Application Bot...")
    
    try:
        subprocess.check_call([sys.executable, "linkedin_job_bot.py"])
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Bot execution failed: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Bot execution interrupted by user")
        return False

def main():
    """Main setup and run function"""
    print("🚀 LinkedIn Job Application Bot - Complete Setup")
    print("=" * 80)
    print("This script will:")
    print("1. Check and install all required dependencies")
    print("2. Verify or create your user profile")
    print("3. Run the LinkedIn job application automation")
    print("=" * 80)
    
    # Step 1: Check dependencies
    print("\n1️⃣ DEPENDENCY CHECK")
    print("-" * 40)
    missing_packages = check_dependencies()
    
    if missing_packages:
        install_choice = input(f"\nInstall {len(missing_packages)} missing packages? (y/N): ").strip().lower()
        if install_choice == 'y':
            if not install_dependencies(missing_packages):
                print("❌ Setup failed - could not install dependencies")
                return
        else:
            print("❌ Setup cancelled - dependencies required")
            return
    
    # Step 2: Check profile
    print("\n2️⃣ PROFILE CHECK")
    print("-" * 40)
    
    if not check_profile():
        create_choice = input("\nCreate profile from resume? (y/N): ").strip().lower()
        if create_choice == 'y':
            if not create_profile():
                print("❌ Setup failed - could not create profile")
                return
        else:
            print("❌ Setup cancelled - profile required")
            return
    
    # Step 3: Run automation
    print("\n3️⃣ AUTOMATION READY")
    print("-" * 40)
    print("✅ All dependencies installed")
    print("✅ User profile configured")
    print("✅ System ready for automation")
    
    run_choice = input("\nStart LinkedIn job application automation? (y/N): ").strip().lower()
    if run_choice == 'y':
        run_linkedin_bot()
    else:
        print("✅ Setup complete - run 'python linkedin_job_bot.py' when ready")
    
    print("\n🎉 SETUP COMPLETED!")
    print("=" * 80)
    print("Your LinkedIn job application bot is ready!")
    print("Run: python linkedin_job_bot.py")
    print("=" * 80)

if __name__ == "__main__":
    main()
