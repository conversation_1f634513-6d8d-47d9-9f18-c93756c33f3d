"""
Daily job application automation scheduler
"""
import asyncio
import schedule
import time
from datetime import datetime, timezone
from typing import List, Dict, Any
from loguru import logger
from config.settings import settings
from core.user_data import UserDataManager
from automation.job_searcher import JobSearcher
from automation.job_applicator import JobApplicator
from integrations.notion_client import NotionJobTracker
from integrations.email_client import EmailNotificationClient

class DailyJobRunner:
    """Orchestrates daily job search and application process"""
    
    def __init__(self):
        self.user_data_manager = UserDataManager()
        self.job_searcher = JobSearcher()
        self.job_applicator = JobApplicator(self.user_data_manager)
        self.notion_tracker = NotionJobTracker()
        self.email_client = EmailNotificationClient()
        
        self.daily_stats = {
            'jobs_found': 0,
            'applications_attempted': 0,
            'applications_successful': 0,
            'applications_failed': 0,
            'errors': []
        }
    
    async def run_daily_job_process(self):
        """Run the complete daily job application process"""
        logger.info("Starting daily job application process")
        start_time = datetime.now()
        
        try:
            # Check if user data is available
            if not self.user_data_manager.load_user_data():
                error_msg = "User data not found. Please complete initial setup."
                logger.error(error_msg)
                await self._send_error_notification(error_msg)
                return
            
            # Check for missing configuration
            missing_fields = self.user_data_manager.check_missing_fields()
            if missing_fields:
                error_msg = f"Missing configuration: {', '.join(missing_fields)}"
                logger.warning(error_msg)
                # Continue with limited functionality
            
            # Step 1: Search for new jobs
            logger.info("Step 1: Searching for new jobs...")
            jobs = await self._search_jobs()
            self.daily_stats['jobs_found'] = len(jobs)
            
            if not jobs:
                logger.info("No new jobs found today")
                await self._send_daily_summary()
                return
            
            # Step 2: Filter applicable jobs
            logger.info("Step 2: Filtering applicable jobs...")
            applicable_jobs = self.job_applicator.filter_applicable_jobs(jobs)
            logger.info(f"Found {len(applicable_jobs)} applicable jobs out of {len(jobs)} total")
            
            # Step 3: Apply to jobs
            if applicable_jobs and settings.max_applications_per_day > 0:
                logger.info("Step 3: Applying to jobs...")
                application_results = await self._apply_to_jobs(applicable_jobs)
                await self._process_application_results(application_results)
            else:
                logger.info("No jobs to apply to or applications disabled")
            
            # Step 4: Send daily summary
            logger.info("Step 4: Sending daily summary...")
            await self._send_daily_summary()
            
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"Daily job process completed in {duration.total_seconds():.1f} seconds")
            
        except Exception as e:
            error_msg = f"Error in daily job process: {e}"
            logger.error(error_msg)
            self.daily_stats['errors'].append(error_msg)
            await self._send_error_notification(error_msg, str(e))
    
    async def _search_jobs(self) -> List[Dict[str, Any]]:
        """Search for jobs across all platforms"""
        try:
            jobs = await self.job_searcher.search_all_platforms()
            
            # Log found jobs to Notion
            for job in jobs:
                try:
                    page_id = self.notion_tracker.add_job_application({
                        'title': job['title'],
                        'company': job['company'],
                        'url': job.get('url', ''),
                        'location': job.get('location', ''),
                        'status': 'Found',
                        'application_method': job.get('application_method', 'Unknown'),
                        'experience_level': job.get('experience_level', ''),
                        'notes': f"Found on {job['platform']} - {job.get('description', '')[:200]}"
                    })
                    
                    if page_id:
                        job['notion_page_id'] = page_id
                        
                except Exception as e:
                    logger.warning(f"Failed to log job to Notion: {e}")
            
            return jobs
            
        except Exception as e:
            logger.error(f"Error searching for jobs: {e}")
            self.daily_stats['errors'].append(f"Job search error: {e}")
            return []
    
    async def _apply_to_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply to filtered jobs"""
        try:
            # Limit jobs based on daily application limit
            jobs_to_apply = jobs[:settings.max_applications_per_day]
            
            logger.info(f"Applying to {len(jobs_to_apply)} jobs")
            
            application_results = await self.job_applicator.apply_to_multiple_jobs(jobs_to_apply)
            
            return application_results
            
        except Exception as e:
            logger.error(f"Error applying to jobs: {e}")
            self.daily_stats['errors'].append(f"Job application error: {e}")
            return []
    
    async def _process_application_results(self, results: List[Dict[str, Any]]):
        """Process and log application results"""
        for result in results:
            self.daily_stats['applications_attempted'] += 1
            
            if result.get('application_success', False):
                self.daily_stats['applications_successful'] += 1
                status = 'Applied'
                
                # Send immediate notification for successful applications
                await self.email_client.send_application_alert(
                    job_title=result['title'],
                    company=result['company'],
                    status='Successfully Applied',
                    details=result.get('application_message', '')
                )
                
            else:
                self.daily_stats['applications_failed'] += 1
                status = 'Error'
                error_details = result.get('application_message', 'Unknown error')
                self.daily_stats['errors'].append(f"Failed to apply to {result['title']}: {error_details}")
            
            # Update Notion record
            if result.get('notion_page_id'):
                try:
                    self.notion_tracker.update_application_status(
                        page_id=result['notion_page_id'],
                        status=status,
                        notes=result.get('application_message', '')
                    )
                except Exception as e:
                    logger.warning(f"Failed to update Notion record: {e}")
    
    async def _send_daily_summary(self):
        """Send daily summary email"""
        try:
            # Get application data from Notion
            successful_apps = self.notion_tracker.get_applications_by_status('Applied')
            failed_apps = self.notion_tracker.get_applications_by_status('Error')
            total_apps = self.notion_tracker.get_daily_application_count()
            
            # Send email summary
            await self.email_client.send_daily_summary(
                applications_today=self.daily_stats['applications_attempted'],
                successful_applications=successful_apps[-10:],  # Last 10 successful
                failed_applications=failed_apps[-10:],  # Last 10 failed
                total_applications=total_apps
            )
            
            logger.info("Daily summary email sent")
            
        except Exception as e:
            logger.error(f"Failed to send daily summary: {e}")
    
    async def _send_error_notification(self, error_message: str, error_details: str = ""):
        """Send error notification email"""
        try:
            await self.email_client.send_error_notification(error_message, error_details)
            logger.info("Error notification sent")
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
    
    def schedule_daily_run(self):
        """Schedule the daily job process"""
        run_time = settings.run_time
        
        schedule.every().day.at(run_time).do(
            lambda: asyncio.create_task(self.run_daily_job_process())
        )
        
        logger.info(f"Daily job process scheduled for {run_time}")
    
    def run_scheduler(self):
        """Run the scheduler loop"""
        logger.info("Starting job application scheduler...")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    async def run_once(self):
        """Run the job process once (for testing or manual execution)"""
        logger.info("Running job process once...")
        await self.run_daily_job_process()

class JobApplicationOrchestrator:
    """Main orchestrator for the job application system"""
    
    def __init__(self):
        self.runner = DailyJobRunner()
    
    async def setup_and_run(self):
        """Setup the system and start the scheduler"""
        logger.info("Setting up Job Application Agent...")
        
        # Check system requirements
        if not await self._check_system_requirements():
            logger.error("System requirements not met. Please check configuration.")
            return
        
        # Setup scheduler
        self.runner.schedule_daily_run()
        
        # Run scheduler
        logger.info("Job Application Agent is now running...")
        self.runner.run_scheduler()
    
    async def run_manual(self):
        """Run the job process manually (one-time execution)"""
        logger.info("Running manual job application process...")
        await self.runner.run_once()
    
    async def _check_system_requirements(self) -> bool:
        """Check if all system requirements are met"""
        requirements_met = True
        
        # Check user data
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            logger.error("User profile not found. Please run initial setup.")
            requirements_met = False
        
        # Check Notion connection
        notion_tracker = NotionJobTracker()
        if not notion_tracker.test_connection():
            logger.warning("Notion connection failed. Job tracking will be limited.")
        
        # Check email configuration
        email_client = EmailNotificationClient()
        if not email_client.test_connection():
            logger.warning("Email configuration failed. Notifications will be disabled.")
        
        # Check Ollama connection
        try:
            import ollama
            client = ollama.Client(host=settings.ollama_host)
            models = client.list()
            if not any(model['name'] == settings.ollama_model for model in models['models']):
                logger.warning(f"Ollama model {settings.ollama_model} not found. Please install it.")
        except Exception as e:
            logger.error(f"Ollama connection failed: {e}")
            requirements_met = False
        
        return requirements_met

if __name__ == "__main__":
    # Test the daily runner
    async def test_runner():
        orchestrator = JobApplicationOrchestrator()
        await orchestrator.run_manual()
    
    # Uncomment to test
    # asyncio.run(test_runner())
