{"automation_method": "browser-use", "llm_model": "qwen2.5:7b", "total_applications": 1, "successful_applications": 1, "applications": [{"title": "Software Engineer Position", "company": "LinkedIn Job Search", "success": true, "applied_at": "2025-06-02T22:32:06.850297", "method": "Easy Apply via browser-use", "note": "Browser-use automation completed - application likely submitted"}], "automation_completed": true, "session_time": "2025-06-02T22:32:06.850311", "raw_result": "AgentHistoryList(all_results=[ActionResult(is_done=False, success=None, extracted_content='🔗  Navigated to https://www.linkedin.com/login', error=None, include_in_memory=True), ActionResult(is_done=False, success=None, extracted_content=None, error=\"Invalid model output format. Please follow the correct schema.\\nDetails: 1 validation error for ActionModel\\nextract_content.goal\\n  Field required [type=missing, input_value={'elements': [3, 7, 8, 10...www.linkedin.com/feed/'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", include_in_memory=True), ActionResult(is_done=False, success=None, extracted_content=None, error=\"Invalid model output format. Please follow the correct schema.\\nDetails: 1 validation error for ActionModel\\nscroll_to_text.text\\n  Field required [type=missing, input_value={'query': 'Amex® Business Gold Card'}, input_type=dict]\\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\", include_in_memor"}