#!/usr/bin/env python3
"""
Test browser-use with Ollama for job searching
"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_browser_use_basic():
    """Test basic browser-use functionality"""
    print("🤖 Testing browser-use with Ollama")
    print("=" * 50)
    
    try:
        # Import browser-use components
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        
        print("✅ browser-use imports successful")
        
        # Initialize Ollama LLM
        llm = ChatOllama(
            model="llama3:latest",  # Use your available model
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        print("✅ Ollama LLM initialized")
        
        # Test simple task
        print("\n🔍 Testing simple web search task...")
        
        agent = Agent(
            task="Go to LinkedIn Jobs and search for 'software engineer' positions. Extract the first 3 job titles and companies you find.",
            llm=llm,
        )
        
        print("🚀 Running browser-use agent...")
        result = await agent.run()
        
        print("✅ Agent completed successfully!")
        print(f"Result: {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ browser-use not available: {e}")
        print("Please install browser-use with Python 3.11+:")
        print("  pip install browser-use")
        return False
    except Exception as e:
        print(f"❌ Error testing browser-use: {e}")
        return False

async def test_job_application():
    """Test job application with browser-use"""
    print("\n🎯 Testing Job Application with browser-use")
    print("=" * 50)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        from core.user_data import UserDataManager
        
        # Load user profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return False
        
        profile = user_manager.profile
        personal_info = profile.personal_info
        
        print(f"✅ Loaded profile for: {personal_info.first_name} {personal_info.last_name}")
        
        # Initialize LLM
        llm = ChatOllama(
            model="llama3:latest",
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        # Create job search task
        task = f"""
        Search for software engineering or cloud engineering jobs suitable for {personal_info.first_name}.
        
        Profile Summary:
        - Name: {personal_info.first_name} {personal_info.last_name}
        - Email: {personal_info.email}
        - Education: Master's in Computer Science
        - Skills: {', '.join(profile.skills[:10])}
        - Experience: {profile.work_experience[0].position} at {profile.work_experience[0].company}
        
        Go to LinkedIn Jobs and search for:
        1. "Software Engineer" OR "Cloud Engineer" 
        2. Location: "Remote" OR "California"
        3. Experience level: "Entry level" OR "Internship"
        
        Find 3-5 relevant job postings and extract:
        - Job title
        - Company name
        - Location
        - Whether it has "Easy Apply"
        - Brief description
        
        Focus on jobs that match the profile's cloud and software engineering background.
        """
        
        print("🚀 Running job search agent...")
        
        agent = Agent(
            task=task,
            llm=llm,
        )
        
        result = await agent.run()
        
        print("✅ Job search completed!")
        print("📋 Results:")
        print("-" * 40)
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in job application test: {e}")
        return False

async def main():
    """Run all browser-use tests"""
    print("🤖 Browser-Use + Ollama Job Agent Test")
    print("=" * 60)
    
    # Test basic functionality
    basic_success = await test_browser_use_basic()
    
    if basic_success:
        # Test job application
        await test_job_application()
        
        print("\n" + "=" * 60)
        print("🎉 BROWSER-USE TESTING COMPLETED!")
        print("=" * 60)
        print("✅ browser-use is working with Ollama")
        print("✅ Job search automation is functional")
        print("✅ Ready for real job applications")
        
        print("\n🚀 Next Steps:")
        print("1. The system can now use browser-use for intelligent job searching")
        print("2. It will automatically navigate job sites and extract information")
        print("3. It can fill out application forms using your profile data")
        print("4. Run the full system with: python3 main.py start --manual")
    else:
        print("\n❌ browser-use not available")
        print("The system will fall back to basic Playwright automation")

if __name__ == "__main__":
    asyncio.run(main())
