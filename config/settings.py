"""
Configuration management for the Job Application Agent
"""
import os
from pathlib import Path
from typing import List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings loaded from environment variables"""

    def __init__(self):
        # Ollama Configuration
        self.ollama_host = os.getenv("OLLAMA_HOST", "http://localhost:11434")
        self.ollama_model = os.getenv("OLLAMA_MODEL", "llama3.2:latest")

        # Notion Integration
        self.notion_api_key = os.getenv("NOTION_API_KEY")
        self.notion_database_id = os.getenv("NOTION_DATABASE_ID")

        # Email Configuration
        self.email_address = os.getenv("EMAIL_ADDRESS")
        self.email_password = os.getenv("EMAIL_PASSWORD")
        self.email_smtp_server = os.getenv("EMAIL_SMTP_SERVER", "smtp.gmail.com")
        self.email_smtp_port = int(os.getenv("EMAIL_SMTP_PORT", "587"))

        # LinkedIn Credentials
        self.linkedin_email = os.getenv("LINKEDIN_EMAIL")
        self.linkedin_password = os.getenv("LINKEDIN_PASSWORD")

        # Security
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        self.data_storage_path = os.getenv("DATA_STORAGE_PATH", "./data/user_data.enc")

        # Job Search Preferences
        self.target_roles = self._parse_list(os.getenv("TARGET_ROLES", "Software Engineer,Cloud Engineer,DevOps Engineer"))
        self.target_locations = self._parse_list(os.getenv("TARGET_LOCATIONS", "Remote,San Francisco,New York"))
        self.experience_level = self._parse_list(os.getenv("EXPERIENCE_LEVEL", "Entry Level,Internship"))
        self.company_size_preference = self._parse_list(os.getenv("COMPANY_SIZE_PREFERENCE", "Startup,Mid-size,Large"))
        self.salary_min = int(os.getenv("SALARY_MIN", "60000")) if os.getenv("SALARY_MIN") else None
        self.salary_max = int(os.getenv("SALARY_MAX", "120000")) if os.getenv("SALARY_MAX") else None

        # Application Settings
        self.max_applications_per_day = int(os.getenv("MAX_APPLICATIONS_PER_DAY", "10"))
        self.apply_to_easy_apply = os.getenv("APPLY_TO_EASY_APPLY", "true").lower() == "true"
        self.apply_to_external_sites = os.getenv("APPLY_TO_EXTERNAL_SITES", "true").lower() == "true"
        self.auto_answer_questions = os.getenv("AUTO_ANSWER_QUESTIONS", "true").lower() == "true"

        # Browser Settings
        self.headless_mode = os.getenv("HEADLESS_MODE", "false").lower() == "true"
        self.browser_timeout = int(os.getenv("BROWSER_TIMEOUT", "30"))
        self.screenshot_on_error = os.getenv("SCREENSHOT_ON_ERROR", "true").lower() == "true"

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file_path = os.getenv("LOG_FILE_PATH", "./logs/job_agent.log")

        # Daily Schedule
        self.run_time = os.getenv("RUN_TIME", "09:00")
        self.timezone = os.getenv("TIMEZONE", "America/Los_Angeles")

        # Resume and Profile
        self.resume_path = os.getenv("RESUME_PATH", "./data/resume.pdf")
        self.cover_letter_template_path = os.getenv("COVER_LETTER_TEMPLATE_PATH", "./data/cover_letter_template.txt")

        # Ensure directories exist
        self._ensure_directories()

    def _parse_list(self, value: str) -> List[str]:
        """Parse comma-separated strings into lists"""
        if not value:
            return []
        return [item.strip() for item in value.split(',') if item.strip()]

    def _ensure_directories(self):
        """Ensure parent directories exist for file paths"""
        paths = [
            self.data_storage_path,
            self.log_file_path,
            self.resume_path,
            self.cover_letter_template_path
        ]

        for path_str in paths:
            if path_str:
                path = Path(path_str)
                path.parent.mkdir(parents=True, exist_ok=True)

# Global settings instance
settings = Settings()

# Create necessary directories
def setup_directories():
    """Create necessary directories for the application"""
    directories = [
        "data",
        "logs",
        "config",
        "core",
        "automation",
        "integrations",
        "utils",
        "scheduler"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

if __name__ == "__main__":
    setup_directories()
    print("Settings loaded successfully!")
    print(f"Ollama Host: {settings.ollama_host}")
    print(f"Target Roles: {settings.target_roles}")
    print(f"Max Applications per Day: {settings.max_applications_per_day}")
