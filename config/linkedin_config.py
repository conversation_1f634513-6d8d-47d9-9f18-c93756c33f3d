"""
LinkedIn configuration and application settings
"""
from typing import Dict, List, Any
from dataclasses import dataclass
import os
from pathlib import Path

@dataclass
class LinkedInConfig:
    """LinkedIn application configuration"""
    
    # Application settings
    max_applications_per_day: int = 10
    delay_between_applications: int = 30  # seconds
    max_retries_per_job: int = 3
    
    # Job search criteria
    target_keywords: List[str] = None
    target_locations: List[str] = None
    experience_levels: List[str] = None
    job_types: List[str] = None
    
    # Application preferences
    prefer_easy_apply: bool = True
    apply_to_company_portals: bool = True
    skip_cover_letter_required: bool = False
    
    # Auto-answer settings
    default_salary_range: str = "$80,000 - $120,000"
    default_start_date: str = "Immediately available"
    default_notice_period: str = "2 weeks"
    visa_sponsorship_needed: bool = False
    willing_to_relocate: bool = True
    preferred_work_arrangement: str = "Remote"
    
    def __post_init__(self):
        if self.target_keywords is None:
            self.target_keywords = [
                "Software Engineer",
                "Cloud Engineer", 
                "DevOps Engineer",
                "Backend Developer",
                "Full Stack Developer",
                "Python Developer",
                "Cloud Developer"
            ]
        
        if self.target_locations is None:
            self.target_locations = [
                "Remote",
                "San Francisco, CA",
                "New York, NY",
                "Seattle, WA",
                "California",
                "United States"
            ]
        
        if self.experience_levels is None:
            self.experience_levels = [
                "Entry level",
                "Internship",
                "Associate"
            ]
        
        if self.job_types is None:
            self.job_types = [
                "Full-time",
                "Contract",
                "Internship"
            ]

class ApplicationAnswers:
    """Standard answers for common application questions"""
    
    @staticmethod
    def get_answer_for_question(question: str, user_profile: Dict[str, Any]) -> str:
        """Get appropriate answer for a given question"""
        question_lower = question.lower()
        
        # Personal information
        if any(keyword in question_lower for keyword in ['name', 'full name']):
            personal = user_profile.get('personal_info', {})
            return f"{personal.get('first_name', '')} {personal.get('last_name', '')}"
        
        if any(keyword in question_lower for keyword in ['email', 'e-mail']):
            return user_profile.get('personal_info', {}).get('email', '')
        
        if any(keyword in question_lower for keyword in ['phone', 'telephone', 'mobile']):
            return user_profile.get('personal_info', {}).get('phone', '')
        
        # Work authorization
        if any(keyword in question_lower for keyword in ['work authorization', 'authorized to work', 'visa', 'sponsorship']):
            return user_profile.get('application_answers', {}).get('work_authorization', 'Authorized to work in the United States')
        
        # Availability
        if any(keyword in question_lower for keyword in ['start date', 'availability', 'when can you start']):
            return user_profile.get('application_answers', {}).get('availability_start_date', 'Immediately available')
        
        # Salary expectations
        if any(keyword in question_lower for keyword in ['salary', 'compensation', 'pay', 'wage']):
            return user_profile.get('application_answers', {}).get('salary_expectation', '$80,000 - $120,000')
        
        # Why interested
        if any(keyword in question_lower for keyword in ['why interested', 'why apply', 'motivation']):
            return user_profile.get('application_answers', {}).get('why_interested', 
                'I am passionate about cloud computing and software engineering. My background in computer science and experience with cloud technologies makes me excited to contribute to innovative projects.')
        
        # Experience level
        if any(keyword in question_lower for keyword in ['experience level', 'years of experience']):
            return "Entry level with relevant internship and project experience"
        
        # Education
        if any(keyword in question_lower for keyword in ['education', 'degree', 'university', 'college']):
            education = user_profile.get('education', [])
            if education:
                edu = education[0]
                return f"{edu.get('degree', '')} in {edu.get('field_of_study', '')} from {edu.get('institution', '')}"
            return "Master's degree in Computer Science"
        
        # Skills
        if any(keyword in question_lower for keyword in ['skills', 'technologies', 'programming']):
            skills = user_profile.get('skills', [])
            return ', '.join(skills[:10]) if skills else "AWS, Azure, Python, C++, Cloud Security"
        
        # Relocation
        if any(keyword in question_lower for keyword in ['relocate', 'relocation', 'move']):
            willing = user_profile.get('application_answers', {}).get('willing_to_relocate', True)
            return "Yes" if willing else "No"
        
        # Work arrangement
        if any(keyword in question_lower for keyword in ['remote', 'work from home', 'hybrid', 'on-site']):
            return user_profile.get('application_answers', {}).get('preferred_work_arrangement', 'Remote')
        
        # Notice period
        if any(keyword in question_lower for keyword in ['notice period', 'notice', 'current job']):
            return "Immediately available"
        
        # Cover letter
        if any(keyword in question_lower for keyword in ['cover letter', 'why should we hire']):
            return user_profile.get('application_answers', {}).get('why_interested', 
                'I bring a strong foundation in computer science, hands-on experience with cloud technologies, and a passion for solving complex problems. My background in testing and development, combined with my eagerness to learn, makes me a valuable addition to your team.')
        
        # Default response for unknown questions
        return "Please see my resume for detailed information."
    
    @staticmethod
    def get_multiple_choice_answer(question: str, options: List[str], user_profile: Dict[str, Any]) -> str:
        """Select appropriate answer from multiple choice options"""
        question_lower = question.lower()
        options_lower = [opt.lower() for opt in options]
        
        # Experience level questions
        if any(keyword in question_lower for keyword in ['experience level', 'years of experience']):
            preferred = ['entry level', '0-1 years', '1-3 years', 'less than 1 year', 'new graduate']
            for pref in preferred:
                for i, opt in enumerate(options_lower):
                    if pref in opt:
                        return options[i]
        
        # Education level
        if any(keyword in question_lower for keyword in ['education', 'degree']):
            preferred = ['master', 'bachelor', 'graduate', 'university']
            for pref in preferred:
                for i, opt in enumerate(options_lower):
                    if pref in opt:
                        return options[i]
        
        # Work authorization
        if any(keyword in question_lower for keyword in ['work authorization', 'visa']):
            preferred = ['authorized', 'citizen', 'permanent resident', 'no sponsorship']
            for pref in preferred:
                for i, opt in enumerate(options_lower):
                    if pref in opt:
                        return options[i]
        
        # Availability
        if any(keyword in question_lower for keyword in ['availability', 'start date']):
            preferred = ['immediately', 'asap', '2 weeks', 'available now']
            for pref in preferred:
                for i, opt in enumerate(options_lower):
                    if pref in opt:
                        return options[i]
        
        # Default to first option if no match
        return options[0] if options else ""

# Default configuration instance
linkedin_config = LinkedInConfig()

# Question patterns and answers
COMMON_QUESTIONS = {
    "work_authorization": [
        "Are you authorized to work in the United States?",
        "Do you require visa sponsorship?",
        "What is your work authorization status?"
    ],
    "availability": [
        "When can you start?",
        "What is your availability?",
        "When would you be available to begin work?"
    ],
    "salary": [
        "What are your salary expectations?",
        "What is your desired salary range?",
        "What compensation are you looking for?"
    ],
    "experience": [
        "How many years of experience do you have?",
        "What is your experience level?",
        "Describe your relevant experience"
    ],
    "relocation": [
        "Are you willing to relocate?",
        "Would you be open to relocation?",
        "Can you relocate for this position?"
    ]
}

# Standard answers
STANDARD_ANSWERS = {
    "work_authorization": "I am authorized to work in the United States and do not require visa sponsorship.",
    "availability": "I am immediately available and can start as soon as possible.",
    "salary": "$80,000 - $120,000 based on the role and location.",
    "experience": "I have entry-level experience with relevant internship and project experience in software development and cloud technologies.",
    "relocation": "Yes, I am willing to relocate for the right opportunity.",
    "why_interested": "I am passionate about cloud computing and software engineering. My background in computer science and hands-on experience with AWS, Azure, and Python makes me excited to contribute to innovative technology solutions.",
    "strengths": "My greatest strength is my ability to quickly learn new technologies and apply them to solve real-world problems. I have strong analytical skills and experience working in cross-functional teams.",
    "career_goals": "My goal is to become a skilled cloud engineer and software developer, working on scalable systems and contributing to cutting-edge technology projects that make a positive impact."
}
