"""
Security utilities for encrypting and decrypting user data
"""
import os
import json
import base64
from typing import Dict, Any, Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from pathlib import Path
from loguru import logger

class SecureDataManager:
    """Handles secure encryption and decryption of user data"""
    
    def __init__(self, encryption_key: Optional[str] = None, storage_path: str = "./data/user_data.enc"):
        self.storage_path = Path(storage_path)
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        if encryption_key:
            self.key = encryption_key.encode()
        else:
            self.key = self._generate_key()
        
        self.fernet = Fernet(base64.urlsafe_b64encode(self.key[:32]))
    
    def _generate_key(self) -> bytes:
        """Generate a new encryption key"""
        password = os.urandom(32)
        salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(password)
        
        # Save salt for future use
        salt_path = self.storage_path.parent / "salt.key"
        with open(salt_path, "wb") as f:
            f.write(salt)
        
        logger.info("Generated new encryption key")
        return key
    
    def encrypt_data(self, data: Dict[str, Any]) -> bool:
        """Encrypt and save user data"""
        try:
            json_data = json.dumps(data, indent=2)
            encrypted_data = self.fernet.encrypt(json_data.encode())
            
            with open(self.storage_path, "wb") as f:
                f.write(encrypted_data)
            
            logger.info(f"Data encrypted and saved to {self.storage_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            return False
    
    def decrypt_data(self) -> Optional[Dict[str, Any]]:
        """Decrypt and load user data"""
        try:
            if not self.storage_path.exists():
                logger.warning("No encrypted data file found")
                return None
            
            with open(self.storage_path, "rb") as f:
                encrypted_data = f.read()
            
            decrypted_data = self.fernet.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode())
            
            logger.info("Data decrypted successfully")
            return data
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            return None
    
    def data_exists(self) -> bool:
        """Check if encrypted data file exists"""
        return self.storage_path.exists()
    
    def backup_data(self, backup_path: Optional[str] = None) -> bool:
        """Create a backup of encrypted data"""
        try:
            if not self.storage_path.exists():
                logger.warning("No data to backup")
                return False
            
            if backup_path is None:
                backup_path = f"{self.storage_path}.backup"
            
            import shutil
            shutil.copy2(self.storage_path, backup_path)
            logger.info(f"Data backed up to {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to backup data: {e}")
            return False
    
    def clear_data(self) -> bool:
        """Securely delete encrypted data"""
        try:
            if self.storage_path.exists():
                self.storage_path.unlink()
                logger.info("Encrypted data cleared")
            return True
        except Exception as e:
            logger.error(f"Failed to clear data: {e}")
            return False

def generate_encryption_key() -> str:
    """Generate a new encryption key for use in environment variables"""
    key = Fernet.generate_key()
    return key.decode()

if __name__ == "__main__":
    # Test the security manager
    manager = SecureDataManager()
    
    # Test data
    test_data = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "555-0123"
    }
    
    # Encrypt
    if manager.encrypt_data(test_data):
        print("✓ Data encrypted successfully")
    
    # Decrypt
    decrypted = manager.decrypt_data()
    if decrypted == test_data:
        print("✓ Data decrypted successfully")
    else:
        print("✗ Data decryption failed")
    
    # Clean up
    manager.clear_data()
    print("✓ Test completed")
