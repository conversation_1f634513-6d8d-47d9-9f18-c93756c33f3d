#!/usr/bin/env python3
"""
Start Ollama and run LinkedIn automation
Ensures Ollama is running before starting automation
"""
import subprocess
import time
import requests
import sys
import asyncio
from pathlib import Path

def check_ollama_running():
    """Check if Ollama is running"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_ollama():
    """Start Ollama server"""
    try:
        print("🚀 Starting Ollama server...")
        process = subprocess.Popen(
            ["ollama", "serve"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        # Wait for Ollama to start
        for i in range(30):  # Wait up to 30 seconds
            if check_ollama_running():
                print("✅ Ollama server started successfully")
                return True
            time.sleep(1)
            print(f"⏳ Waiting for Ollama to start... ({i+1}/30)")
        
        print("❌ Ollama failed to start within 30 seconds")
        return False
        
    except FileNotFoundError:
        print("❌ Ollama not found. Please install Ollama first:")
        print("   Visit: https://ollama.ai")
        return False
    except Exception as e:
        print(f"❌ Error starting Ollama: {e}")
        return False

def ensure_model_available():
    """Ensure qwen2.5:7b model is available"""
    try:
        print("🔍 Checking for qwen2.5:7b model...")
        result = subprocess.run(
            ["ollama", "list"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if "qwen2.5:7b" in result.stdout:
            print("✅ qwen2.5:7b model available")
            return True
        
        print("📥 Pulling qwen2.5:7b model...")
        print("⏳ This may take a few minutes...")
        
        result = subprocess.run(
            ["ollama", "pull", "qwen2.5:7b"],
            timeout=600  # 10 minutes timeout
        )
        
        if result.returncode == 0:
            print("✅ qwen2.5:7b model downloaded successfully")
            return True
        else:
            print("❌ Failed to download qwen2.5:7b model")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Model download timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking/downloading model: {e}")
        return False

async def run_linkedin_automation():
    """Run the LinkedIn automation"""
    try:
        # Import and run the automation
        sys.path.append(str(Path(__file__).parent))
        
        from simple_linkedin_automation import SimpleLinkedInBot
        
        print("\n🤖 Initializing LinkedIn automation...")
        bot = SimpleLinkedInBot()
        
        # Check if LLM is working
        if not bot.llm:
            print("❌ LLM not available")
            return
        
        print("✅ LLM connection successful")
        
        # Load stored credentials
        credentials = bot.load_credentials()
        if not credentials:
            print("❌ No stored credentials found")
            print("Please run the automation first to store credentials:")
            print("python simple_linkedin_automation.py")
            return
        
        print(f"✅ Loaded credentials for: {credentials['email']}")
        
        # Run automation with stored credentials
        print("\n🚀 Starting automated job application...")
        results = await bot.run_automation(
            credentials['email'], 
            credentials['password'], 
            max_applications=5
        )
        
        # Display results
        bot.display_results(results)
        
    except Exception as e:
        print(f"❌ Automation error: {e}")

def main():
    """Main function"""
    print("🤖 LinkedIn Automation Startup Script")
    print("=" * 50)
    print("This script will:")
    print("1. Start Ollama server")
    print("2. Ensure qwen2.5:7b model is available")
    print("3. Run LinkedIn job application automation")
    print("=" * 50)
    
    # Step 1: Check/Start Ollama
    if not check_ollama_running():
        if not start_ollama():
            print("❌ Failed to start Ollama. Exiting.")
            return
    else:
        print("✅ Ollama is already running")
    
    # Step 2: Ensure model is available
    if not ensure_model_available():
        print("❌ Model setup failed. Exiting.")
        return
    
    # Step 3: Run automation
    print("\n" + "=" * 50)
    print("🎉 SYSTEM READY - STARTING AUTOMATION")
    print("=" * 50)
    
    asyncio.run(run_linkedin_automation())

if __name__ == "__main__":
    main()
