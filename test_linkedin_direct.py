#!/usr/bin/env python3
"""
Direct LinkedIn test with improved prompting to fix the navigation issue
"""
import sys
import asyncio
from pathlib import Path
from getpass import getpass

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_direct_linkedin_navigation():
    """Test direct LinkedIn navigation with very specific instructions"""
    print("🎯 Direct LinkedIn Navigation Test")
    print("=" * 50)
    print("This test addresses the navigation issue where the LLM goes to example.com")
    print("instead of LinkedIn. We'll use very specific, direct instructions.")
    print("=" * 50)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        
        # Configure LLM for better instruction following
        llm = ChatOllama(
            model="llama3:latest",
            base_url="http://localhost:11434",
            temperature=0.0,  # Very deterministic
            top_p=0.1,        # Focused responses
            repeat_penalty=1.2
        )
        
        print("✅ LLM configured with low temperature for better instruction following")
        
        # Very direct and specific task
        task = """
CRITICAL TASK: Navigate to LinkedIn Login Page

YOU MUST DO EXACTLY THIS:
1. Navigate to this EXACT URL: https://www.linkedin.com/login
2. DO NOT go to example.com or any other website
3. The URL you must visit is: https://www.linkedin.com/login
4. Once there, take a screenshot and describe what you see

IMPORTANT:
- The correct URL is: https://www.linkedin.com/login
- NOT example.com
- NOT any other website
- ONLY https://www.linkedin.com/login

STEP 1: Navigate to https://www.linkedin.com/login
STEP 2: Confirm you are on the LinkedIn login page
STEP 3: Describe what login fields you see

START NOW: Go to https://www.linkedin.com/login
"""
        
        print("🚀 Starting navigation test...")
        print("Task: Navigate directly to LinkedIn login page")
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("\n📊 Navigation Test Results:")
        print("=" * 40)
        
        result_str = str(result).lower()
        
        # Check if it went to the right place
        if 'linkedin.com/login' in result_str:
            print("✅ SUCCESS: Agent navigated to LinkedIn login page")
        elif 'linkedin.com' in result_str:
            print("⚠️ PARTIAL: Agent went to LinkedIn but maybe not login page")
        elif 'example.com' in result_str:
            print("❌ FAILED: Agent went to example.com instead of LinkedIn")
        else:
            print("❓ UNCLEAR: Navigation result unclear")
        
        print(f"\nResult details: {str(result)[:500]}...")
        
        return 'linkedin.com' in result_str
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_linkedin_login_with_credentials():
    """Test LinkedIn login with real credentials using improved prompting"""
    print("\n🔐 LinkedIn Login Test with Real Credentials")
    print("=" * 50)
    
    try:
        from browser_use import Agent
        from langchain_ollama import ChatOllama
        from core.user_data import UserDataManager
        
        # Load profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return False
        
        profile = user_manager.profile
        print(f"✅ Profile loaded: {profile.personal_info.first_name} {profile.personal_info.last_name}")
        
        # Get credentials
        linkedin_email = input("LinkedIn Email: ").strip()
        linkedin_password = getpass("LinkedIn Password: ").strip()
        
        if not linkedin_email or not linkedin_password:
            print("❌ Credentials required")
            return False
        
        # Configure LLM
        llm = ChatOllama(
            model="llama3:latest",
            base_url="http://localhost:11434",
            temperature=0.0,
            top_p=0.1
        )
        
        # Very specific login task
        task = f"""
LINKEDIN LOGIN TASK

STEP 1: Navigate to https://www.linkedin.com/login
- You MUST go to this exact URL: https://www.linkedin.com/login
- Do NOT go to example.com or any other site

STEP 2: Enter Login Credentials
- Find the email/username input field
- Enter this email: {linkedin_email}
- Find the password input field  
- Enter this password: {linkedin_password}

STEP 3: Submit Login
- Click the "Sign in" button
- Wait for the page to load

STEP 4: Verify Success
- Check if you're on LinkedIn homepage/feed
- Look for profile picture or navigation menu
- Confirm successful login

CREDENTIALS:
Email: {linkedin_email}
Password: {linkedin_password}

CRITICAL: Start by going to https://www.linkedin.com/login (NOT example.com)
"""
        
        print("🚀 Starting LinkedIn login test...")
        
        agent = Agent(task=task, llm=llm)
        result = await agent.run()
        
        print("\n📊 Login Test Results:")
        print("=" * 40)
        
        result_str = str(result).lower()
        
        # Check login success
        if any(indicator in result_str for indicator in ['linkedin.com/feed', 'linkedin home', 'profile picture']):
            print("✅ SUCCESS: Login appears successful!")
            return True
        elif 'linkedin.com/login' in result_str and 'sign in' in result_str:
            print("⚠️ PARTIAL: Reached login page but may not have completed login")
            return False
        elif 'example.com' in result_str:
            print("❌ FAILED: Went to example.com instead of LinkedIn")
            return False
        else:
            print("❓ UNCLEAR: Login result unclear")
            print(f"Result: {str(result)[:300]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_job_application():
    """Test applying to a specific job"""
    print("\n💼 Job Application Test")
    print("=" * 50)
    
    job_url = input("Enter LinkedIn job URL to test: ").strip()
    if not job_url:
        print("❌ No job URL provided")
        return
    
    try:
        from automation.improved_linkedin_applicator import ImprovedLinkedInApplicator
        from core.user_data import UserDataManager
        
        # Load profile
        user_manager = UserDataManager()
        if not user_manager.load_user_data():
            print("❌ No user profile found")
            return
        
        profile = user_manager.profile
        
        # Convert to dict
        user_profile_dict = {
            "personal_info": {
                "first_name": profile.personal_info.first_name,
                "last_name": profile.personal_info.last_name,
                "email": profile.personal_info.email,
                "phone": profile.personal_info.phone,
                "city": profile.personal_info.city,
                "state": profile.personal_info.state
            },
            "application_answers": {
                "work_authorization": profile.application_answers.work_authorization,
                "availability_start_date": profile.application_answers.availability_start_date,
                "salary_expectation": profile.application_answers.salary_expectation,
                "why_interested": profile.application_answers.why_interested
            },
            "skills": profile.skills
        }
        
        # Dummy credentials for testing (won't be used for job application)
        applicator = ImprovedLinkedInApplicator(user_profile_dict, "<EMAIL>", "password")
        
        print(f"🚀 Testing job application to: {job_url}")
        
        result = await applicator.apply_to_job_url(job_url)
        
        print("\n📊 Application Test Results:")
        print("=" * 40)
        print(f"Success: {'✅ Yes' if result['success'] else '❌ No'}")
        print(f"Job URL: {result['job_url']}")
        print(f"Applied at: {result['applied_at']}")
        
        if 'error' in result:
            print(f"Error: {result['error']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def main():
    """Main test function"""
    print("🤖 LinkedIn Automation - Direct Testing")
    print("=" * 60)
    print("This addresses the issue where LLM goes to example.com instead of LinkedIn")
    print("=" * 60)
    
    print("\nChoose test:")
    print("1. Test direct LinkedIn navigation (fix example.com issue)")
    print("2. Test LinkedIn login with credentials")
    print("3. Test job application to specific URL")
    
    choice = input("\nEnter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        success = await test_direct_linkedin_navigation()
        if success:
            print("\n✅ Navigation test passed! The LLM can now navigate to LinkedIn correctly.")
        else:
            print("\n❌ Navigation test failed. The LLM still has issues with navigation.")
    
    elif choice == "2":
        success = await test_linkedin_login_with_credentials()
        if success:
            print("\n✅ Login test passed! Ready for job applications.")
        else:
            print("\n❌ Login test failed. Check credentials and try again.")
    
    elif choice == "3":
        await test_job_application()
    
    else:
        print("❌ Invalid choice")
    
    print("\n🎯 Summary:")
    print("The main issue was that the LLM (llama3) wasn't following navigation instructions precisely.")
    print("Solutions implemented:")
    print("✅ Lower temperature (0.0) for more deterministic behavior")
    print("✅ More specific and direct instructions")
    print("✅ Explicit URL repetition in prompts")
    print("✅ Better error detection and handling")

if __name__ == "__main__":
    asyncio.run(main())
