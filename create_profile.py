#!/usr/bin/env python3
"""
Create user profile from resume data
"""
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from core.user_data import UserDataManager, PersonalInfo, WorkExperience, Education, ApplicationAnswers, UserProfile

def create_hemanth_profile():
    """Create <PERSON><PERSON><PERSON>'s profile from resume data"""
    
    # Personal Information
    personal_info = PersonalInfo(
        first_name="<PERSON><PERSON><PERSON>",
        last_name="<PERSON><PERSON>",
        email="<EMAIL>",
        phone="(*************",
        address="1660 Kendall Drive",
        city="San Bernardino",
        state="California",
        zip_code="92407",
        country="United States",
        linkedin_url="https://linkedin.com/in/hemanth-kiran-reddy-polu",
        github_url="",
        portfolio_url=""
    )
    
    # Work Experience
    work_experience = [
        WorkExperience(
            company="Tech Mahindra",
            position="Manual Test Engineer",
            start_date="06/2021",
            end_date="11/2022",
            description="Developed comprehensive test plans, scripts, and use cases for manual testing of applications on Mainframe using JCL commands. Analyzed test results to verify system performance and managed defects by following up and reporting. Participated in cross-functional testing calls to understand functionalities and features for end-to-end (E2E) flow testing.",
            technologies=["JCL", "Mainframe Testing", "Manual Testing", "Agile"],
            is_current=False
        )
    ]
    
    # Education
    education = [
        Education(
            institution="California State University, San Bernardino",
            degree="Master of Science (M.S.)",
            field_of_study="Computer Science",
            graduation_date="05/2025",
            gpa=3.6
        ),
        Education(
            institution="Sathyabama University",
            degree="Bachelor of Engineering (B.E.)",
            field_of_study="Computer Science and Engineering",
            graduation_date="03/2020",
            gpa=None
        )
    ]
    
    # Skills
    skills = [
        "AWS", "GCP", "Microsoft Azure", "Azure Functions", "Cloud Security",
        "Manual Testing", "Test Plan Development", "JCL", "SQL", "Python", "C++",
        "GitHub", "REST APIs", "Elasticsearch", "FAISS", "Natural Language Processing",
        "Agile Methodologies", "Amazon Textract", "Amazon Rekognition", "Amazon SageMaker",
        "scikit-learn", "Hugging Face Transformers"
    ]
    
    # Certifications
    certifications = [
        "Microsoft Certified: Azure Fundamentals",
        "Hackerrank Certified: Java (Basic)",
        "Using Python to Interact with the Operating System",
        "Cyber Threat Management"
    ]
    
    # Application Answers
    application_answers = ApplicationAnswers(
        why_interested="I am passionate about cloud computing, software testing, and cybersecurity. As a graduate student in Computer Science with hands-on experience in Azure, AWS, and GCP, I am excited to apply my technical skills in real-world scenarios. My experience as a Manual Test Engineer at Tech Mahindra, combined with my academic projects involving AI, cloud platforms, and security systems, has prepared me to contribute effectively to software engineering and cloud engineering roles.",
        greatest_strength="My greatest strength is my ability to bridge theoretical knowledge with practical application. Through my academic projects like building RAG systems with Elasticsearch and FAISS, and my professional experience in manual testing at Tech Mahindra, I have developed strong analytical and problem-solving skills. I excel at working in cross-functional teams and have proven ability to deliver technical projects involving cloud platforms, AI, and security systems.",
        biggest_weakness="I am sometimes overly detail-oriented, which can slow down my initial progress on projects. However, I have been working on balancing thoroughness with efficiency by setting time-boxed goals and focusing on MVP (Minimum Viable Product) approaches first, then iterating for improvements.",
        career_goals="My career goal is to become a skilled cloud engineer or software engineer specializing in cloud security and AI systems. I want to work on large-scale distributed systems, contribute to cloud infrastructure design, and develop AI-powered solutions that solve real-world problems. Long-term, I aspire to lead technical teams and architect cloud-native applications that are secure, scalable, and efficient.",
        why_leaving_current_job="I am currently completing my Master's degree in Computer Science and actively seeking full-time opportunities to apply my enhanced technical skills in cloud computing, AI, and software development. I am looking for a role that will allow me to grow professionally and contribute to innovative technology solutions.",
        salary_expectation="$80,000 - $120,000 based on role and location",
        availability_start_date="Immediately available (graduating May 2025)",
        work_authorization="Authorized to work in the United States",
        willing_to_relocate=True,
        preferred_work_arrangement="Remote"
    )
    
    # Create complete profile
    profile = UserProfile(
        personal_info=personal_info,
        work_experience=work_experience,
        education=education,
        skills=skills,
        certifications=certifications,
        application_answers=application_answers,
        resume_path="/Users/<USER>/Documents/Projects/test/HemanthKiranReddyPolu_SoftwareEngineer.pdf",
        cover_letter_template=None,
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat()
    )
    
    return profile

def main():
    """Create and save Hemanth's profile"""
    print("Creating Hemanth's profile from resume data...")
    
    try:
        # Create profile
        profile = create_hemanth_profile()
        
        # Initialize user data manager and save
        user_manager = UserDataManager()
        user_manager.profile = profile
        
        if user_manager.save_user_data():
            print("✅ Profile created and saved successfully!")
            print("\nProfile Summary:")
            user_manager.display_profile_summary()
            
            # Check for missing fields
            missing_fields = user_manager.check_missing_fields()
            if missing_fields:
                print(f"\n⚠️ Missing configuration: {', '.join(missing_fields)}")
            else:
                print("\n✅ All required fields are configured!")
                
        else:
            print("❌ Failed to save profile")
            
    except Exception as e:
        print(f"❌ Error creating profile: {e}")

if __name__ == "__main__":
    main()
