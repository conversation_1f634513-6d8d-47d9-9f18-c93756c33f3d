#!/usr/bin/env python3
"""
Dry Run Test for Job Application Agent
Tests all components without actually applying to jobs or sending emails
"""
import sys
import asyncio
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
sys.path.append(str(Path(__file__).parent))

console = Console()

def test_imports():
    """Test if all required modules can be imported"""
    console.print("\n[bold blue]🔍 Testing Imports[/bold blue]")
    
    tests = [
        ("config.settings", "Configuration management"),
        ("core.user_data", "User data management"),
        ("core.resume_parser", "Resume parser"),
        ("automation.job_searcher", "Job searcher"),
        ("automation.job_applicator", "Job applicator"),
        ("integrations.notion_client", "Notion integration"),
        ("integrations.email_client", "Email client"),
        ("utils.security", "Security utilities"),
        ("scheduler.daily_runner", "Daily scheduler"),
    ]
    
    results = []
    
    for module_name, description in tests:
        try:
            __import__(module_name)
            results.append((module_name, description, "✅ Pass", "green"))
        except ImportError as e:
            results.append((module_name, description, f"❌ Fail: {e}", "red"))
        except Exception as e:
            results.append((module_name, description, f"⚠️ Error: {e}", "yellow"))
    
    # Display results
    table = Table(title="Import Test Results")
    table.add_column("Module", style="cyan")
    table.add_column("Description", style="white")
    table.add_column("Status", style="bold")
    
    for module, desc, status, color in results:
        table.add_row(module, desc, f"[{color}]{status}[/{color}]")
    
    console.print(table)
    
    # Summary
    passed = sum(1 for _, _, status, _ in results if "Pass" in status)
    total = len(results)
    
    if passed == total:
        console.print(f"\n[green]✅ All {total} imports successful![/green]")
        return True
    else:
        console.print(f"\n[red]❌ {total - passed} imports failed out of {total}[/red]")
        return False

def test_configuration():
    """Test configuration loading"""
    console.print("\n[bold blue]⚙️ Testing Configuration[/bold blue]")
    
    try:
        from config.settings import settings
        
        config_tests = [
            ("Ollama Host", settings.ollama_host, settings.ollama_host.startswith("http")),
            ("Ollama Model", settings.ollama_model, len(settings.ollama_model) > 0),
            ("Target Roles", settings.target_roles, len(settings.target_roles) > 0),
            ("Target Locations", settings.target_locations, len(settings.target_locations) > 0),
            ("Max Applications", settings.max_applications_per_day, settings.max_applications_per_day > 0),
            ("Log Level", settings.log_level, settings.log_level in ["DEBUG", "INFO", "WARNING", "ERROR"]),
        ]
        
        table = Table(title="Configuration Test Results")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="white")
        table.add_column("Status", style="bold")
        
        all_passed = True
        
        for name, value, test_result in config_tests:
            status = "✅ Valid" if test_result else "❌ Invalid"
            color = "green" if test_result else "red"
            
            if not test_result:
                all_passed = False
            
            # Truncate long values
            display_value = str(value)
            if len(display_value) > 50:
                display_value = display_value[:47] + "..."
            
            table.add_row(name, display_value, f"[{color}]{status}[/{color}]")
        
        console.print(table)
        
        if all_passed:
            console.print("[green]✅ Configuration valid![/green]")
        else:
            console.print("[yellow]⚠️ Some configuration issues found[/yellow]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Configuration test failed: {e}[/red]")
        return False

def test_security():
    """Test security utilities"""
    console.print("\n[bold blue]🔒 Testing Security[/bold blue]")
    
    try:
        from utils.security import SecureDataManager, generate_encryption_key
        
        # Test key generation
        key = generate_encryption_key()
        console.print(f"[green]✅ Encryption key generated: {key[:20]}...[/green]")
        
        # Test data manager
        manager = SecureDataManager()
        
        # Test encryption/decryption
        test_data = {"test": "data", "number": 123}
        
        if manager.encrypt_data(test_data):
            console.print("[green]✅ Data encryption successful[/green]")
            
            decrypted = manager.decrypt_data()
            if decrypted == test_data:
                console.print("[green]✅ Data decryption successful[/green]")
            else:
                console.print("[red]❌ Data decryption failed[/red]")
                return False
        else:
            console.print("[red]❌ Data encryption failed[/red]")
            return False
        
        # Cleanup
        manager.clear_data()
        console.print("[green]✅ Security test completed[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Security test failed: {e}[/red]")
        return False

def test_resume_parser():
    """Test resume parser"""
    console.print("\n[bold blue]📄 Testing Resume Parser[/bold blue]")
    
    try:
        from core.resume_parser import ResumeParser
        
        parser = ResumeParser()
        
        # Test with sample text
        sample_text = """
        John Doe
        <EMAIL>
        (555) 123-4567
        Software Engineer with 3 years of experience
        Skills: Python, JavaScript, React, AWS, Docker
        linkedin.com/in/johndoe
        github.com/johndoe
        """
        
        # Test contact info extraction
        contact_info = parser.parse_contact_info(sample_text)
        console.print(f"[cyan]Contact Info:[/cyan] {contact_info}")
        
        # Test skills extraction
        skills = parser.parse_skills(sample_text)
        console.print(f"[cyan]Skills Found:[/cyan] {skills}")
        
        # Test experience parsing
        experience_years = parser.parse_experience_years(sample_text)
        console.print(f"[cyan]Experience Years:[/cyan] {experience_years}")
        
        if contact_info and skills:
            console.print("[green]✅ Resume parser working correctly[/green]")
            return True
        else:
            console.print("[yellow]⚠️ Resume parser has limited functionality[/yellow]")
            return True
            
    except Exception as e:
        console.print(f"[red]❌ Resume parser test failed: {e}[/red]")
        return False

def test_user_data():
    """Test user data management"""
    console.print("\n[bold blue]👤 Testing User Data Management[/bold blue]")
    
    try:
        from core.user_data import UserDataManager, PersonalInfo, ApplicationAnswers
        
        manager = UserDataManager()
        
        # Test data models
        personal_info = PersonalInfo(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="************",
            address="123 Main St",
            city="San Francisco",
            state="CA",
            zip_code="94105",
            country="United States"
        )
        
        app_answers = ApplicationAnswers(
            why_interested="Passionate about technology",
            greatest_strength="Problem solving",
            biggest_weakness="Perfectionism",
            career_goals="Senior engineer role",
            availability_start_date="Immediately",
            work_authorization="Authorized to work in US",
            willing_to_relocate=False,
            preferred_work_arrangement="Remote"
        )
        
        console.print("[green]✅ User data models created successfully[/green]")
        console.print(f"[cyan]Personal Info:[/cyan] {personal_info.first_name} {personal_info.last_name}")
        console.print(f"[cyan]Work Authorization:[/cyan] {app_answers.work_authorization}")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ User data test failed: {e}[/red]")
        return False

async def test_job_searcher():
    """Test job searcher (without actual web requests)"""
    console.print("\n[bold blue]🔍 Testing Job Searcher[/bold blue]")
    
    try:
        from automation.job_searcher import JobSearcher
        
        searcher = JobSearcher()
        
        # Test AI response parsing
        sample_ai_response = '''
        [
            {
                "title": "Software Engineer",
                "company": "Tech Corp",
                "location": "Remote",
                "url": "https://example.com/job1",
                "experience_level": "Entry Level",
                "salary_range": "$80,000 - $120,000",
                "description": "Great opportunity for new graduates",
                "application_method": "Easy Apply"
            },
            {
                "title": "Cloud Engineer",
                "company": "Cloud Solutions Inc",
                "location": "San Francisco, CA",
                "url": "https://example.com/job2",
                "experience_level": "Mid Level",
                "salary_range": "$100,000 - $150,000",
                "description": "Work with AWS and Kubernetes",
                "application_method": "External"
            }
        ]
        '''
        
        jobs = searcher._parse_ai_job_response(sample_ai_response, "LinkedIn")
        
        if jobs and len(jobs) >= 2:
            console.print(f"[green]✅ Parsed {len(jobs)} jobs from AI response[/green]")
            for i, job in enumerate(jobs, 1):
                console.print(f"[cyan]Job {i}:[/cyan] {job['title']} at {job['company']}")
            return True
        else:
            console.print("[yellow]⚠️ Job parsing had issues but component loaded[/yellow]")
            return True
            
    except Exception as e:
        console.print(f"[red]❌ Job searcher test failed: {e}[/red]")
        return False

def test_notion_client():
    """Test Notion client (without actual API calls)"""
    console.print("\n[bold blue]📊 Testing Notion Client[/bold blue]")
    
    try:
        from integrations.notion_client import NotionJobTracker
        
        # Test without actual API key
        tracker = NotionJobTracker(api_key="test_key", database_id="test_db")
        
        # Test data extraction methods
        sample_page = {
            "id": "test_id",
            "properties": {
                "Job Title": {
                    "title": [{"text": {"content": "Software Engineer"}}]
                },
                "Company": {
                    "rich_text": [{"text": {"content": "Tech Corp"}}]
                },
                "Status": {
                    "select": {"name": "Applied"}
                }
            }
        }
        
        extracted_data = tracker._extract_page_data(sample_page)
        
        if extracted_data and extracted_data["title"] == "Software Engineer":
            console.print("[green]✅ Notion data extraction working[/green]")
            console.print(f"[cyan]Extracted:[/cyan] {extracted_data}")
            return True
        else:
            console.print("[yellow]⚠️ Notion client loaded but data extraction issues[/yellow]")
            return True
            
    except Exception as e:
        console.print(f"[red]❌ Notion client test failed: {e}[/red]")
        return False

def test_email_client():
    """Test email client (without sending emails)"""
    console.print("\n[bold blue]📧 Testing Email Client[/bold blue]")
    
    try:
        from integrations.email_client import EmailNotificationClient
        
        # Test without actual credentials
        client = EmailNotificationClient(
            email_address="<EMAIL>",
            email_password="test_password"
        )
        
        # Test HTML generation
        html_content = client._create_daily_summary_html(
            applications_today=5,
            successful_applications=[
                {"title": "Software Engineer", "company": "Tech Corp", "location": "Remote", "application_method": "Easy Apply"}
            ],
            failed_applications=[
                {"title": "Cloud Engineer", "company": "Cloud Inc", "error_details": "Form submission failed"}
            ],
            total_applications=25
        )
        
        if html_content and "Software Engineer" in html_content:
            console.print("[green]✅ Email HTML generation working[/green]")
            console.print(f"[cyan]HTML Length:[/cyan] {len(html_content)} characters")
            return True
        else:
            console.print("[yellow]⚠️ Email client loaded but HTML generation issues[/yellow]")
            return True
            
    except Exception as e:
        console.print(f"[red]❌ Email client test failed: {e}[/red]")
        return False

async def run_dry_run():
    """Run complete dry run test"""
    console.print(Panel.fit(
        "[bold blue]🤖 Job Application Agent - Dry Run Test[/bold blue]\n"
        "Testing all components without external API calls or automation",
        border_style="blue"
    ))
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Security", test_security),
        ("Resume Parser", test_resume_parser),
        ("User Data", test_user_data),
        ("Job Searcher", test_job_searcher),
        ("Notion Client", test_notion_client),
        ("Email Client", test_email_client),
    ]
    
    results = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        for test_name, test_func in tests:
            task = progress.add_task(f"Testing {test_name}...", total=None)
            
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                results.append((test_name, result))
            except Exception as e:
                console.print(f"[red]❌ {test_name} test crashed: {e}[/red]")
                results.append((test_name, False))
            
            progress.remove_task(task)
    
    # Summary
    console.print("\n[bold blue]📋 Test Summary[/bold blue]")
    
    summary_table = Table(title="Dry Run Results")
    summary_table.add_column("Component", style="cyan")
    summary_table.add_column("Status", style="bold")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if result:
            summary_table.add_row(test_name, "[green]✅ Pass[/green]")
            passed += 1
        else:
            summary_table.add_row(test_name, "[red]❌ Fail[/red]")
    
    console.print(summary_table)
    
    # Final result
    if passed == total:
        console.print(f"\n[bold green]🎉 All {total} tests passed! System ready for deployment.[/bold green]")
    elif passed >= total * 0.8:
        console.print(f"\n[bold yellow]⚠️ {passed}/{total} tests passed. System mostly functional with minor issues.[/bold yellow]")
    else:
        console.print(f"\n[bold red]❌ Only {passed}/{total} tests passed. System needs attention before deployment.[/bold red]")
    
    console.print(f"\n[cyan]Next steps:[/cyan]")
    console.print("1. Install missing dependencies if any tests failed")
    console.print("2. Configure .env file with your credentials")
    console.print("3. Run: python main.py setup")
    console.print("4. Run: python main.py start --manual")

if __name__ == "__main__":
    asyncio.run(run_dry_run())
